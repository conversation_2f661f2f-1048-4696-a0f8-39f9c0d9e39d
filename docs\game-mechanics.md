# Game Mechanics

Core game mechanics of Outcast MUD: attributes, combat, magic, skills, equipment, systems. This version aligns with the codebase (e.g., THAC0-style to-hit math, spell memorization, MCCP-driven IO does not affect combat cadence).

## Character Attributes

### Primary

- STR: Melee damage, carry, bash/grapple checks
- INT: Spell learning, mana pool, some skill learning rates
- WIS: Mana regen, save vs. spells/divine
- DEX: Armor Class contribution, initiative-like ordering, dodge
- CON: Hit points and recovery
- CHA: Shop prices, leadership/followers, select interactions

### Secondary

- HP: Health
- Mana: Spell resource
- Move: Movement points
- Armor Class (AC): Lower is better (harder to hit)
- THAC0: To-Hit vs AC metric (lower is better to hit)
- Saves: Categories such as paralysis/rod/petrification/breath/spell

## Combat

Cadence is tick-driven; typical “rounds” progress in pulses handled by the main loop.

Flow:
1) Attacker attempts to hit using THAC0 vs target AC
2) On success, damage = weapon dice and modifiers (e.g., STR, magic, skills)
3) Special attacks/skills may modify to-hit, damage, or state
4) Effects/affects are applied and expire per tick

Common attacks:
- <PERSON><PERSON> (primary hand, possibly off-hand if supported)
- Ranged (bows/thrown; availability depends on areas)
- Backstab (thief)
- Bash/Kick (warrior)
- Circle (thief specialization)

Damage types:
- Physical: slash, pierce, bludgeon
- Elemental: fire, cold, electricity, acid
- Magic/Divine

Resistances/immunities come from race, gear, effects, or spells.

## Magic

Schools (typical D&D lineage):
- Abjuration, Alteration, Conjuration, Divination, Enchantment, Evocation, Illusion, Necromancy

Memorization:
- Caster classes memorize spells into slots; time and availability scale by level/class
- Multiple copies of spells can be memorized
- Cleric-style recovery via prayer/meditation where applicable

Casting constraints:
- Mana must be available
- Verbal components blocked by silence
- Somatic components blocked when bound/restrained
- Some spells require material components (server/area dependent)
- Interrupt on damage or certain conditions

## Skills

Categories:
- Combat: weapon profs, dodge, parry
- Thief: sneak, hide, steal, pick locks
- Magic-related: scribe, brew, enchant
- General: climb, swim, track, scan
- Craft: forge, repair, identify

Improvement:
- Practice at trainers (sessions)
- Some usage-based learning
- Class/race/level gates

## Equipment

Slots:
- Light
- Head; Neck x2
- Body; Arms; Hands
- Waist; Legs; Feet
- Finger x2
- Weapon; Shield/Held
- About body; Floating (if supported)

Properties:
- +hit/+dam, AC, stat modifiers, resistances
- Flags such as glow, hum, magic, nodrop, noremove, alignment restrictions

Durability:
- Quality tiers from Excellent → Broken
- Repair via forge skill or NPCs

## Death and Recovery

At 0 HP:
- Stun window (-1 to -9), then death at -10
- Corpse with equipment is created
- Respawn at hometown temple
- Retrieve corpse before decay to recover items

XP loss:
- On death; magnitude scales by level/context
- Death does not delevel

## Alignment

Scale:
- Good: 1000..350
- Neutral: 349..-349
- Evil: -350..-1000

Effects:
- Item restrictions
- Spell availability/effectiveness
- NPC reactions, association eligibility
- City access or guard reactions in some areas

## Associations

Examples include combat guilds, magic orders, and other organizations. Benefits typically include access to halls, equipment, abilities, events, and political influence. Exact names and availability may vary by deployment and content set.

## Economy

Currency:
- Gold pieces primarily; some areas may reference copper/platinum/regionals (area-dependent)

Trading:
- NPC merchants with buy/sell modifiers and inventory limits
- Player auction systems and ad-hoc barter boards if enabled
- Player shops where supported by area/content

## Time and Weather

Time:
- 24-hour cycle; day/night impacts visibility and spawns
- Shops and services may observe hours

Weather:
- Rain/snow/wind affecting movement, tracking, or ranged
- Temperature implications in select areas

## PvP

Rules:
- Typically level-band restrictions
- Safe rooms disallow combat
- Looting policies and justice systems vary by server configuration and zones

Zones:
- Designated PvP regions, clan war areas, arenas

## Special Systems

Contracts:
- Player-driven tasks with automated rewards and tracking (if enabled)

Ships:
- Sea travel between ports; weather can influence navigation and combat

Planar Travel:
- Elemental planes, astral hubs; altered rulesets per plane

Disease:
- Spread via combat/environment; cured via spells or time; some flagged magical

Note:
- Availability and rules can vary with server configuration, active areas, and content modules. Consult in-game help for authoritative command syntax and specifics.

This document summarizes mechanics consistent with the repository’s systems and typical Circle-derived behavior. Adjust values and availability as you customize areas or server rules.
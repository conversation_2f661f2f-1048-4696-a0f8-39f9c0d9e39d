# Network Protocol Documentation

Outcast MUD uses standard Telnet with optional MCCP v2 compression and ANSI color codes. The server links against zlib (per Makefile: LIBS = -lz). Database libraries are not involved in networking by default.

## Table of Contents

1. [Connection Overview](#connection-overview)
2. [Telnet Protocol](#telnet-protocol)
3. [MCCP Compression](#mccp-compression)
4. [Color Codes](#color-codes)
5. [Input Processing](#input-processing)
6. [Output Buffering](#output-buffering)
7. [Special Protocols](#special-protocols)
8. [Client Implementation](#client-implementation)

## Connection Overview

### Connection Flow

1. Client connects to server on TCP port
2. Server sends greeting message  
3. <PERSON><PERSON> enters username/password
4. After authentication, game loop begins
5. <PERSON><PERSON> sends commands, server sends responses
6. Connection closes on quit or disconnect

### Connection States

```c
#define CON_PLAYING     0   // Normal gameplay
#define CON_CLOSE       1   // Disconnect pending
#define CON_GET_NAME    2   // Getting character name
#define CON_NAME_CNFRM  3   // Confirm new name
#define CON_PASSWORD    4   // Getting password
#define CON_NEWPASSWD   5   // New character password
#define CON_CNFPASSWD   6   // Confirm password
#define CON_QSEX        7   // Select sex
#define CON_QCLASS      8   // Select class
#define CON_RMOTD       9   // Reading MOTD
#define CON_MENU        10  // Main menu
#define CON_EXDESC      11  // Extra description
#define CON_CHPWD_1     12  // Changing password
#define CON_CHPWD_2     13  // New password
#define CON_CHPWD_3     14  // Confirm new password
#define CON_DELCNF1     15  // Delete confirmation 1
#define CON_DELCNF2     16  // Delete confirmation 2
```

## Telnet Protocol

Baseline RFC 854 Telnet with common options. Examples below reflect actual negotiation used by the server.

### Supported Telnet Commands

```
IAC  = 255  // Interpret As Command
DONT = 254  // Don't use option
DO   = 253  // Do use option
WONT = 252  // Won't use option
WILL = 251  // Will use option
SB   = 250  // Subnegotiation begin
SE   = 240  // Subnegotiation end
GA   = 249  // Go Ahead
EL   = 248  // Erase Line
EC   = 247  // Erase Character
NOP  = 241  // No Operation
```

### Telnet Options

```
TELOPT_ECHO    = 1   // Echo option
TELOPT_SGA     = 3   // Suppress Go Ahead
TELOPT_NAWS    = 31  // Window Size
TELOPT_TTYPE   = 24  // Terminal Type
TELOPT_COMPRESS = 85  // MCCP v1
TELOPT_COMPRESS2 = 86 // MCCP v2
```

### Negotiation Sequences

```
// Server initiates echo off for password
Server: IAC WILL ECHO
Client: IAC DO ECHO

// Server turns echo back on
Server: IAC WONT ECHO
Client: IAC DONT ECHO

// MCCP v2 negotiation
Server: IAC WILL COMPRESS2
Client: IAC DO COMPRESS2
Server: IAC SB COMPRESS2 IAC SE
// Compression begins after SE
```

## MCCP Compression

Mud Client Compression Protocol (MCCP) v2 via zlib reduces bandwidth usage significantly (often 70–90%). The server only compresses outbound data after the COMPRESS2 subnegotiation completes (IAC SB 86 IAC SE).

### MCCP v2 Implementation

```c
// Enable compression
void start_compression(struct descriptor_data *d) {
    z_stream *stream = &d->comp->stream;
    
    // Send negotiation
    write_to_descriptor(d->descriptor, compress_will2);
    
    // Initialize zlib
    stream->zalloc = Z_NULL;
    stream->zfree = Z_NULL;
    stream->opaque = Z_NULL;
    
    deflateInit(stream, Z_DEFAULT_COMPRESSION);
    
    // Send start sequence
    write_to_descriptor(d->descriptor, compress_start);
    
    d->comp->state = 1; // Compression active
}
```

### Compression States

1. **Negotiation**: WILL/DO exchange
2. **Initialization**: Send IAC SB COMPRESS2 IAC SE
3. **Active**: All output compressed with zlib
4. **Termination**: Clean shutdown on disconnect

## Color Codes

Outcast uses custom inline color tokens that are translated to ANSI escape sequences before being sent to the client.

### Color Code Format

```
&+<code> - Foreground color
&-<code> - Background color
&N       - Normal (reset)
&B       - Bold
&I       - Italic
&U       - Underline
&F       - Flash/Blink
```

### Color Mappings

```
Basic Colors:
&+L - Black       &-L - Black background
&+R - Red         &-R - Red background
&+G - Green       &-G - Green background
&+Y - Yellow      &-Y - Yellow background
&+B - Blue        &-B - Blue background
&+M - Magenta     &-M - Magenta background
&+C - Cyan        &-C - Cyan background
&+W - White       &-W - White background

Bright Colors:
&+l - Dark Gray   &-l - Dark Gray background
&+r - Light Red   &-r - Light Red background
&+g - Light Green &-g - Light Green background
&+y - Light Yellow &-y - Light Yellow background
&+b - Light Blue  &-b - Light Blue background
&+m - Light Magenta &-m - Light Magenta background
&+c - Light Cyan  &-c - Light Cyan background
&+w - Bright White &-w - Bright White background
```

### ANSI Escape Sequences

```
ESC[0m  - Reset all attributes
ESC[1m  - Bold
ESC[3m  - Italic
ESC[4m  - Underline
ESC[5m  - Blink
ESC[30-37m - Foreground colors
ESC[40-47m - Background colors
ESC[90-97m - Bright foreground colors
ESC[100-107m - Bright background colors
```

## Input Processing

Server-side pipeline overview and key limits used by the descriptor layer.
### Input Buffer Management

```c
#define MAX_INPUT_LENGTH  256
#define MAX_RAW_INPUT_LENGTH  512

struct descriptor_data {
    char inbuf[MAX_RAW_INPUT_LENGTH];
    char last_input[MAX_INPUT_LENGTH];
    int bufptr;
    int buflen;
};
```

### Input Processing Flow

1. **Read from Socket**: Raw bytes into buffer
2. **Telnet Processing**: Handle IAC sequences
3. **Line Assembly**: Build complete command lines
4. **Command Queue**: Queue completed commands
5. **Execution**: Process commands in game loop

### Special Input Handling

- **Backspace**: Handle BS (8) and DEL (127)
- **Line Editing**: Support for basic editing
- **History**: Command history with arrow keys (if client supports)
- **Input Echo**: Controlled via telnet options

## Output Buffering

Descriptor output buffering, prompt emission, ANSI expansion, and optional compression.
### Output Buffer Structure

```c
#define LARGE_BUFSIZE  (MAX_STRING_LENGTH * 2)
#define SMALL_BUFSIZE  1024

struct descriptor_data {
    char *output;        // Output buffer
    char small_outbuf[SMALL_BUFSIZE];
    int bufspace;        // Space left in buffer
    int bufptr;          // Current position
    bool has_prompt;     // Prompt pending
};
```

### Output Processing

1. **Write to Buffer**: Game output accumulated
2. **Process Colors**: Convert color codes to ANSI
3. **Page Breaking**: Handle paged output
4. **Compression**: Apply MCCP if active
5. **Socket Write**: Send to client

### Prompt Handling

```
// Normal prompt
HP: 100/100 Mana: 50/50 Move: 80/80>

// Combat prompt
HP: 100/100 Mana: 50/50 Move: 80/80> [Fighting: Goblin]

// Custom prompts supported via tokens:
%h - current hp     %H - max hp
%m - current mana   %M - max mana
%v - current move   %V - max move
%g - gold carried   %r - room vnum
%e - exits          %t - game time
```

## Special Protocols

Availability depends on build flags and runtime configuration (and client capability).
### MXP (MUD eXtension Protocol)

Basic MXP support for enhanced client features:

```xml
<!-- Clickable exits -->
<SEND HREF="north">North</SEND>

<!-- Item links -->
<SEND HREF="look sword">a sharp sword</SEND>

<!-- Custom elements -->
<!ELEMENT Hp FLAG="Set hp">
<Hp>100/150</Hp>
```

### GMCP (Generic MUD Communication Protocol)

JSON-based out-of-band communication:

```json
// Character vitals
Core.Vitals {
    "hp": 100,
    "maxhp": 100,
    "mp": 50,
    "maxmp": 50,
    "mv": 80,
    "maxmv": 80
}

// Room information
Room.Info {
    "num": 3001,
    "name": "The Temple Square",
    "exits": {"north": 3002, "south": 3000}
}
```

### MSP (MUD Sound Protocol)

Sound trigger support:

```
!!SOUND(thunder.wav V=80 L=1 P=50)
!!MUSIC(combat.mid V=50 L=-1 C=1)
```

## Client Notes

Minimal examples are illustrative; prefer a client with native Telnet/MCCP/ANSI support (Mudlet, TinTin++, MUSHclient). If scripting your own, ensure correct Telnet option handling and MCCP decompression.
### Minimal Telnet Client

```python
import socket
import select

def connect_mud(host, port):
    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    sock.connect((host, port))
    
    while True:
        # Check for data from server
        ready = select.select([sock], [], [], 0.1)
        if ready[0]:
            data = sock.recv(4096)
            if not data:
                break
            print(data.decode('ascii', errors='ignore'), end='')
        
        # Check for user input
        ready = select.select([sys.stdin], [], [], 0.1)
        if ready[0]:
            line = input()
            sock.send((line + '\r\n').encode('ascii'))
    
    sock.close()
```

### Handling Color Codes

```python
def parse_color_codes(text):
    # Color code to ANSI mapping
    color_map = {
        '&+L': '\033[30m',  # Black
        '&+R': '\033[31m',  # Red
        '&+G': '\033[32m',  # Green
        '&+Y': '\033[33m',  # Yellow
        '&+B': '\033[34m',  # Blue
        '&+M': '\033[35m',  # Magenta
        '&+C': '\033[36m',  # Cyan
        '&+W': '\033[37m',  # White
        '&N': '\033[0m',    # Reset
    }
    
    for code, ansi in color_map.items():
        text = text.replace(code, ansi)
    
    return text
```

### MCCP Support

```python
import zlib

class MCCPConnection:
    def __init__(self):
        self.compressed = False
        self.decompressor = None
    
    def handle_telnet(self, data):
        # Check for MCCP start sequence
        if b'\xFF\xFA\x56\xFF\xF0' in data:
            pos = data.find(b'\xFF\xFA\x56\xFF\xF0')
            # Everything after this is compressed
            self.compressed = True
            self.decompressor = zlib.decompressobj()
            return data[:pos+5], data[pos+5:]
        return data, b''
    
    def decompress(self, data):
        if self.compressed and data:
            return self.decompressor.decompress(data)
        return data
```

## Connection Optimizations

### Bandwidth Optimization

1. **MCCP**: 70-90% compression ratio
2. **Brief Mode**: Reduced combat spam
3. **Compact Mode**: Minimal descriptions
4. **Quiet Mode**: No channel messages

### Latency Handling

1. **Command Queuing**: Multiple commands per packet
2. **Predictive Echo**: Client-side echo
3. **Smart Prompts**: Only send on change
4. **Output Bundling**: Combine small messages

### Security Considerations

1. **Input Validation**: Strict length limits
2. **Telnet Sanitization**: Filter control codes
3. **Rate Limiting**: Command flood protection
4. **Idle Timeout**: Automatic disconnection

## Debugging Network Issues

### Common Problems

1. **Echo Issues**: Check telnet negotiation
2. **Color Bleeding**: Ensure proper resets
3. **Compression Errors**: Verify MCCP state
4. **Input Loss**: Check buffer overflow
5. **Lag Spikes**: Monitor output buffer size

### Network Logging

```c
// Enable network debugging
#define LOG_NETWORK 1

void log_network(const char *format, ...) {
    #ifdef LOG_NETWORK
    va_list args;
    va_start(args, format);
    vfprintf(network_log, format, args);
    va_end(args);
    #endif
}
```

This protocol documentation provides the foundation for implementing MUD clients or understanding the network layer of Outcast MUD. The combination of standard Telnet with MCCP compression and color codes creates an efficient text-based gaming protocol.
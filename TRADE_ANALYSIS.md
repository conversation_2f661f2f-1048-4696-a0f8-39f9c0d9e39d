Outcast MUD Trade System — Research and Proposed Economy

Source basis
- Code: trade.c and trade.h
  - Initialization and loading of per-type tables: trade.c (initialize_trade)
  - Computation of production/usage from those tables: trade.c (load_trade_center, update_trade_center)
  - Pricing model: trade.c (get_commodity_value)
  - Commodity indices/constants and MAX_COMMODITIES: trade.h
- Data: 9 trade type files in Players/Trade/*.trd, each with 94 integers (pairs for 46 commodities, production/consumption multipliers), plus two trailing integers.

1) MAX_COMMODITIES and the 46 commodities
MAX_COMMODITIES = 46 (trade.h:62). The commodities and printable names come from trade.h constants and trade.c’s commodity_names/short_comm_names arrays.

Index → Short name (trade.c short_comm_names) → Themed display name (trade.c commodity_names)
0: gold → &+Ygold bar&N
1: silver → &+Wsilver bar&N
2: copper → &+ycopper bar&N
3: platinum → &+Cplatinum bar&N
4: iron → &+Liron bar&N
5: bronze → &+Ybronze bar&N
6: adamantium → &+cadamantium bar&N
7: mithril → &+bmithril bar&N
8: steel → &+Lsteel bar&N
9: lead → &+Llead bar&N
10: tin → &+Ltin bar&N
11: diamond → &+Wbag of diamonds&N
12: emerald → &+Gbag of emeralds&N
13: ruby → &+Rbag of rubies&N
14: meat → &+ybarrel of meat&N
15: salt → &+Wbarrel of salt&N
16: spices → &+msack of spices&N
17: milk → &+Wbarrel of milk&N
18: ale → &+Ycask of ale&N
19: beer → &+Ycask of beer&N
20: wine → &+mcask of wine&N
21: zzar → &+Mcask of zzar&N
22: fruits → &+rbarrel of fruits&N
23: wheat → &+ybarrel of wheat&N
24: corn → &+ybarrel of corn&N
25: fish → &+cbarrel of fish&N
26: oil → &+gbarrel of olive oil&N
27: sugar → &+ybarrel of sugar&N
28: honey → &+Ybarrel of honey&N
29: marble → &+Gslab of marble&N
30: granite → &+Lslab of granite&N
31: coal → &+Lbarrel of coal&N
32: sulphur → &+Rbarrel of sulphur&N
33: timber → &+ytrunk of timber&N
34: silk → &+Broll of silk fabric&N
35: wool → &+Wroll of wool fabric&N
36: linen → &+Yroll of linen fabric&N
37: furs → &+ypack of furs&N
38: glass → &+Cslab of glass&N
39: leather → &+yroll of leather&N
40: parchment → &+Yroll of parchment&N
41: papyrus → &+Wroll of papyrus&N
42: dye → &+Rbag of dye&N
43: coffee → &+Lbarrel of coffee&N
44: tea → &+Ybarrel of tea&N
45: tobacco → &+ybarrel of tobacco&N
46: industrial product → &+Lindustrial product&N

Note: 0..45 are tradeable objects (items GOLD_VNUM..TOBACCO_VNUM), and index 46 is a derived “Industrial product” category for the economy calculations.

2) Mapping of 94 integers per file to commodity production/consumption
Each .trd file is read into a 94-int array for a given “economy type” (Industrial, Agricoltural, Commercial, Mineral, IndComm, IndMin, MinAgr, MinComm, AgrComm).

- Code path: initialize_trade loads files as sequences of “%d,%*s\n” lines into arrays:
  - Industrial[] (trade.c:1071–1080)
  - Agricoltural[] (1082–1091)
  - Commercial[] (1093–1102)
  - Mineral[] (1104–1113)
  - IndComm[] (1115–1124)
  - IndMin[] (1126–1135)
  - MinAgr[] (1137–1146)
  - MinComm[] (1148–1157)
  - AgrComm[] (1159–1168)

- How these 94 numbers are used:
  - In load_trade_center and update_trade_center, a pointer tv is set to the selected array based on tc->type (trade.c:922–951, 994–1025).
  - For count in 0..MAX_COMMODITIES:
    - Production base ratio = tv[count*2] * tc->population / 5000
      - stored as ti[count].production with minimum of 1 (trade.c:955–963)
    - Usage base ratio = tv[count*2+1] * tc->population / 5000
      - stored as ti[count].usage with minimum of 1 (trade.c:960–963)
    - Multipliers and expectations start at 100 and can be perturbed by random events (trade_center_random).
  - Special case for index 46 (industrial product) in update_trade_center:
    - Uses mineral satisfaction: production = tv[46*2] * population * mineral_satisf / 5000 (trade.c:1045–1047).
    - For all others, production = tv[index*2] * population / 5000.

Therefore:
- Lines 1..92 map to 46 pairs (P, C): for commodity i, use tv[2*i] as production weight and tv[2*i+1] as usage weight.
- Lines 93..94 correspond to 2 extra integers read into the array (indices 92 and 93). The code doesn’t directly consume beyond 2*(MAX_COMMODITIES+1)-1 = 93, so these two values belong to the last commodity pair: commodity index 46 has P at 93 (2*46) and C at 94 (2*46+1). This is why the loop reads “<= MAX_COMMODITIES * 2 + 2” (trade.c:1078 etc.). The last “+2” accommodates the existence of index 46 (industrial product), yielding 94 integers total:
  - 47 commodities (0..46) × 2 fields (P, C) = 94.

Summary:
- Indexing in the 94-int array tv is:
  - For commodity i in 0..46:
    - Production weight = tv[2*i]
    - Consumption (usage) weight = tv[2*i+1]

3) Price/value calculation and trade formula
Item base values are defined in comm_values[0..46] (trade.c:347–395). This is the core price vector in copper coins.

Per-transaction value calculation is in get_commodity_value(obj, type, trade_center, store_room, sell_buy) (trade.c:415–455):

- If obj is given, the commodity type is inferred from obj’s vnum (obj_vnum - GOLD_VNUM) (lines 421–424).
- Compute multiplier M:
  M = (usage * usage_multi/100 * usage_expectation/100 * 1.5) 
      / (in_store + 1 + production * prod_multi/100 * prod_expectation/100 * 1.5)

- Clamp M to [0.7, 1.3] (lines 430–433).
- For buy vs sell:
  - If BUY (the shop buys from player), M *= 0.75 (line 436). This lowers what the shop pays vs its sell price.
  - If SELL (the shop sells to player), M can be nudged up based on production/in_store ratio (lines 438–451). If production greatly exceeds inventory ratio, add up to +50% to M. If ratio < 1, apply 0.9 discount.
- Final price = comm_values[type] * M.

Implications:
- Trade center prices respond to supply/demand through production, usage, inventory, and multipliers/expectations (random events).
- The 0.7–1.3 clamp prevents runaway prices.
- Shops buy at 75% of derived price; sells use the ratio-based nudges.

4) Town types and economic meaning
Town types are enumerated (trade.h:119–127). Each town type selects which 94-int table tv to use (trade.c:922–951, 994–1025). The tv table sets the baseline for production and usage per commodity:

- INDUSTRIAL:
  - Should emphasize higher production weights for manufactured goods and processed materials, including industrial product (index 46), steel, glass, leather, parchment, papyrus, textiles (silk, wool, linen), dyes.
  - Consumption tends to be higher for raw inputs: metals, coal, sulphur, timber, oils, grains/food to sustain workforce. Industrial product production is also influenced by mineral_satisf at runtime.

- AGRICOLTURAL (sic):
  - Should emphasize higher production weights for food/agri commodities: meat, milk, fruits, wheat, corn, fish (if coastal), oil (olive), sugar, honey. Also perhaps wool, leather.
  - Lower production of ores/metals; higher consumption of finished goods compared to their production.

- COMMERCIAL:
  - Balanced, trade hub characteristics:
    - Moderate production of most categories.
    - Moderate consumption of most categories.
    - Could have more inventory throughput events (by design via random events and mobtrade), but here we only configure weights. Keep balanced to avoid extreme surpluses/deficits.

- MINERAL:
  - High production for ores/minerals/metals: iron, copper, tin, lead, coal, sulphur, precious metals (gold, silver, platinum), gemstones.
  - Lower production for agri goods; higher consumption of food and some manufactured products.
  - Industrial product (46) base weight should be reasonable; ultimate production depends on mineral_satisf (trade.c:1045–1047).

5) Relationship tables: IndComm, IndMin, MinAgr, MinComm, AgrComm
There are nine total tables with the four primary archetypes and five “relationship” types:
- Primary: Industrial, Agricoltural, Commercial, Mineral
- Relationship: IndComm, IndMin, MinAgr, MinComm, AgrComm

The code treats them identically — they are simply alternative tv tables selected by tc->type. There’s no special logic beyond selecting the table.

Therefore, the five relationship files are intended to represent hybrid or transitional economies:
- IndComm: Industrial-Commercial hybrid
- IndMin: Industrial-Mineral hybrid
- MinAgr: Mineral-Agricoltural hybrid
- MinComm: Mineral-Commercial hybrid
- AgrComm: Agricoltural-Commercial hybrid

Each should be tuned as weighted blends between their respective archetypes (e.g., IndComm roughly between Industrial and Commercial), possibly with minor specializations.

6) Proposed integer weights for all 9 trade files
Design goals
- Keep economy stable but dynamic under the 0.7–1.3 price clamp.
- Ensure each archetype has clear export strengths and import needs.
- Avoid zero weights (the code forces min 1 after scaling, but zeros at tv mean near-zero when population is low). Non-zero small integers provide a baseline.
- Use population scaling: production/usage = weight × population / 5000, clamped min 1. For a typical population range ~1,000–50,000 (trade.c:989–992), weights in the range 1..20 are sensible.
- Keep industrial product (46) production weight present in all types, but make it more significant in Industrial, IndComm, IndMin, Commercial; less in Agricoltural and Mineral.

Commodity grouping for tuning
Metals & ores & minerals: gold(0), silver(1), copper(2), platinum(3), iron(4), bronze(5), adamantium(6), mithril(7), steel(8), lead(9), tin(10), coal(31), sulphur(32), marble(29), granite(30), gemstones(11..13)
Agricultural & food: meat(14), salt(15), spices(16), milk(17), ale(18), beer(19), wine(20), zzar(21), fruits(22), wheat(23), corn(24), fish(25), oil(26), sugar(27), honey(28), coffee(43), tea(44), tobacco(45)
Materials/processed: timber(33), silk(34), wool(35), linen(36), furs(37), glass(38), leather(39), parchment(40), papyrus(41), dye(42)
Industrial product: 46

Notation
For each type we provide Production Weight (P) and Consumption Weight (C) per commodity index i (0..46). The file should list each weight on a separate line as “number,comment”. The engine only reads the integer, but comments help maintainability.

To keep this document concise, we provide structured patterns and explicit numeric sets per type that can be copied into the 94-line files. Values are integers.

A. Industrial.trd
Intent: strong producers of processed goods, moderate production of metals (for inputs), high consumption of agri foods.

Metals and inputs:
- Iron(4) P=10 C=5
- Copper(2) P=7 C=5
- Tin(10) P=6 C=5
- Lead(9) P=4 C=4
- Coal(31) P=8 C=4
- Sulphur(32) P=5 C=3
- Steel(8) P=9 C=6
- Bronze(5) P=6 C=4
- Gold/Silver/Platinum/Gems mostly consume or small production: P=1 C=2
- Adamantium(6), Mithril(7) rare: P=1 C=3

Agricultural:
- Meat(14) P=3 C=8
- Milk(17) P=3 C=7
- Wheat(23), Corn(24): P=3 C=9
- Fruits(22) P=2 C=6
- Fish(25) P=2 C=5
- Oil(26) P=2 C=6
- Sugar(27) P=2 C=6
- Honey(28) P=1 C=4
- Salt(15) P=2 C=5
- Spices(16) P=1 C=4
- Drinks (18..21): P=2 C=6
- Coffee(43), Tea(44), Tobacco(45): P=1 C=4

Materials/processed:
- Timber(33) P=4 C=6
- Glass(38) P=7 C=4
- Leather(39) P=6 C=4
- Parchment(40) P=5 C=3
- Papyrus(41) P=3 C=3
- Silk(34) P=4 C=3
- Wool(35) P=3 C=3
- Linen(36) P=5 C=3
- Furs(37) P=2 C=3
- Dye(42) P=5 C=3

Stone:
- Marble(29) P=3 C=3
- Granite(30) P=4 C=3

Precious metals/gems group explicitly:
- gold(0) P=1 C=2
- silver(1) P=1 C=2
- platinum(3) P=1 C=2
- diamond(11) P=1 C=2
- emerald(12) P=1 C=2
- ruby(13) P=1 C=2

Industrial product(46):
- P=12 C=6

Full numeric list for Industrial (order 0..46, providing P,C pairs):

0 gold: 1,2
1 silver: 1,2
2 copper: 7,5
3 platinum: 1,2
4 iron: 10,5
5 bronze: 6,4
6 adamantium: 1,3
7 mithril: 1,3
8 steel: 9,6
9 lead: 4,4
10 tin: 6,5
11 diamond: 1,2
12 emerald: 1,2
13 ruby: 1,2
14 meat: 3,8
15 salt: 2,5
16 spices: 1,4
17 milk: 3,7
18 ale: 2,6
19 beer: 2,6
20 wine: 2,6
21 zzar: 2,6
22 fruits: 2,6
23 wheat: 3,9
24 corn: 3,9
25 fish: 2,5
26 oil: 2,6
27 sugar: 2,6
28 honey: 1,4
29 marble: 3,3
30 granite: 4,3
31 coal: 8,4
32 sulphur: 5,3
33 timber: 4,6
34 silk: 4,3
35 wool: 3,3
36 linen: 5,3
37 furs: 2,3
38 glass: 7,4
39 leather: 6,4
40 parchment: 5,3
41 papyrus: 3,3
42 dye: 5,3
43 coffee: 1,4
44 tea: 1,4
45 tobacco: 1,4
46 industrial product: 12,6

B. Agricoltural.trd
Intent: strong food production, lower metals, consumes manufactured goods.

- Metals and minerals: light production P=1..2, consumption C=3..4
- Food: strong production, moderate consumption
- Materials like wool/leather high production (livestock)
- Industrial product: low P, moderate C

Numbers:

0 gold: 1,2
1 silver: 1,2
2 copper: 1,3
3 platinum: 1,2
4 iron: 1,3
5 bronze: 1,3
6 adamantium: 1,2
7 mithril: 1,2
8 steel: 1,3
9 lead: 1,3
10 tin: 1,3
11 diamond: 1,1
12 emerald: 1,1
13 ruby: 1,1
14 meat: 10,5
15 salt: 3,3
16 spices: 2,3
17 milk: 9,5
18 ale: 6,4
19 beer: 6,4
20 wine: 6,4
21 zzar: 2,3
22 fruits: 8,4
23 wheat: 10,6
24 corn: 10,6
25 fish: 5,4
26 oil: 6,4
27 sugar: 4,3
28 honey: 5,3
29 marble: 1,2
30 granite: 1,2
31 coal: 1,3
32 sulphur: 1,2
33 timber: 4,4
34 silk: 3,3
35 wool: 8,4
36 linen: 4,4
37 furs: 4,3
38 glass: 1,3
39 leather: 6,4
40 parchment: 2,3
41 papyrus: 3,3
42 dye: 2,3
43 coffee: 5,3
44 tea: 5,3
45 tobacco: 5,3
46 industrial product: 2,5

C. Commercial.trd
Intent: balanced hub, moderate production and consumption across the board. Slightly higher consumption than production for most to encourage import/export flow.

Use P=3, C=4 baseline, with modest deviations:

0 gold: 2,3
1 silver: 2,3
2 copper: 3,4
3 platinum: 2,3
4 iron: 3,4
5 bronze: 3,4
6 adamantium: 1,2
7 mithril: 1,2
8 steel: 3,4
9 lead: 2,3
10 tin: 2,3
11 diamond: 2,2
12 emerald: 2,2
13 ruby: 2,2
14 meat: 4,4
15 salt: 3,4
16 spices: 3,4
17 milk: 4,4
18 ale: 4,4
19 beer: 4,4
20 wine: 4,4
21 zzar: 3,4
22 fruits: 4,4
23 wheat: 4,5
24 corn: 4,5
25 fish: 3,4
26 oil: 3,4
27 sugar: 3,4
28 honey: 3,4
29 marble: 2,3
30 granite: 2,3
31 coal: 3,4
32 sulphur: 2,3
33 timber: 3,4
34 silk: 3,3
35 wool: 3,3
36 linen: 3,3
37 furs: 3,3
38 glass: 3,4
39 leather: 3,4
40 parchment: 3,4
41 papyrus: 3,4
42 dye: 3,4
43 coffee: 3,4
44 tea: 3,4
45 tobacco: 3,4
46 industrial product: 4,4

D. Mineral.trd
Intent: strong mineral production, consumes food and many finished goods. Industrial product moderate (depends on mineral_satisf).

0 gold: 6,2
1 silver: 5,2
2 copper: 8,3
3 platinum: 3,2
4 iron: 12,4
5 bronze: 4,3
6 adamantium: 2,2
7 mithril: 2,2
8 steel: 3,5
9 lead: 6,3
10 tin: 6,3
11 diamond: 3,2
12 emerald: 3,2
13 ruby: 3,2
14 meat: 3,8
15 salt: 2,5
16 spices: 1,4
17 milk: 2,6
18 ale: 2,6
19 beer: 2,6
20 wine: 2,6
21 zzar: 1,4
22 fruits: 2,6
23 wheat: 2,8
24 corn: 2,8
25 fish: 2,5
26 oil: 2,6
27 sugar: 1,5
28 honey: 1,4
29 marble: 7,3
30 granite: 8,3
31 coal: 12,4
32 sulphur: 9,3
33 timber: 2,6
34 silk: 1,4
35 wool: 2,5
36 linen: 2,5
37 furs: 2,4
38 glass: 2,5
39 leather: 2,5
40 parchment: 1,4
41 papyrus: 1,4
42 dye: 1,4
43 coffee: 1,4
44 tea: 1,4
45 tobacco: 1,4
46 industrial product: 4,6

E. IndComm.trd (Industrial-Commercial hybrid)
Blend of Industrial and Commercial. Take approximate midpoint and round:

0: 1,2
1: 1,2
2: 5,5
3: 1,2
4: 7,5
5: 5,4
6: 1,3
7: 1,3
8: 6,5
9: 3,4
10: 4,4
11: 1,2
12: 1,2
13: 1,2
14: 3,8
15: 2,5
16: 2,4
17: 3,6
18: 3,5
19: 3,5
20: 3,5
21: 2,5
22: 3,5
23: 3,7
24: 3,7
25: 2,5
26: 2,5
27: 3,5
28: 2,4
29: 3,3
30: 3,3
31: 6,4
32: 4,3
33: 4,5
34: 4,3
35: 3,3
36: 4,3
37: 2,3
38: 5,4
39: 5,4
40: 4,3
41: 3,3
42: 4,3
43: 2,4
44: 2,4
45: 2,4
46: 8,5

F. IndMin.trd (Industrial-Mineral hybrid)
Strong metals and processing, weaker agri:

0: 3,2
1: 3,2
2: 8,4
3: 2,2
4: 11,5
5: 5,4
6: 2,3
7: 2,3
8: 7,6
9: 5,4
10: 6,4
11: 2,2
12: 2,2
13: 2,2
14: 3,8
15: 2,5
16: 1,4
17: 3,7
18: 2,6
19: 2,6
20: 2,6
21: 1,4
22: 2,6
23: 3,8
24: 3,8
25: 2,5
26: 2,6
27: 2,6
28: 1,4
29: 5,3
30: 6,3
31: 10,4
32: 7,3
33: 3,6
34: 3,3
35: 3,3
36: 4,3
37: 2,3
38: 5,5
39: 5,4
40: 4,3
41: 3,3
42: 4,3
43: 1,4
44: 1,4
45: 1,4
46: 9,6

G. MinAgr.trd (Mineral-Agricoltural hybrid)
Strong in minerals and foods, weak in manufactured goods:

0: 4,2
1: 3,2
2: 7,3
3: 2,2
4: 10,4
5: 4,3
6: 2,2
7: 2,2
8: 2,5
9: 4,3
10: 4,3
11: 2,2
12: 2,2
13: 2,2
14: 9,5
15: 3,3
16: 2,3
17: 8,5
18: 6,4
19: 6,4
20: 5,4
21: 2,3
22: 7,4
23: 9,5
24: 9,5
25: 4,4
26: 5,4
27: 3,3
28: 4,3
29: 5,3
30: 6,3
31: 9,4
32: 7,3
33: 3,5
34: 2,3
35: 6,4
36: 4,4
37: 3,3
38: 2,4
39: 3,4
40: 2,3
41: 3,3
42: 2,3
43: 4,3
44: 4,3
45: 4,3
46: 3,5

H. MinComm.trd (Mineral-Commercial hybrid)
Moderate minerals with balanced consumption:

0: 4,2
1: 3,2
2: 6,3
3: 2,2
4: 9,4
5: 4,3
6: 2,2
7: 2,2
8: 3,5
9: 4,3
10: 4,3
11: 2,2
12: 2,2
13: 2,2
14: 4,5
15: 3,4
16: 3,4
17: 4,4
18: 3,4
19: 3,4
20: 3,4
21: 2,4
22: 4,4
23: 4,5
24: 4,5
25: 3,4
26: 3,4
27: 3,4
28: 3,4
29: 4,3
30: 5,3
31: 8,4
32: 6,3
33: 3,4
34: 2,3
35: 3,3
36: 3,3
37: 3,3
38: 3,4
39: 3,4
40: 3,4
41: 3,4
42: 3,4
43: 2,4
44: 2,4
45: 2,4
46: 5,5

I. AgrComm.trd (Agricoltural-Commercial hybrid)
Moderate foods with balanced trade; consumes finished goods moderately.

0: 1,2
1: 1,2
2: 2,3
3: 1,2
4: 1,3
5: 1,3
6: 1,2
7: 1,2
8: 1,3
9: 1,3
10: 1,3
11: 1,1
12: 1,1
13: 1,1
14: 9,5
15: 3,3
16: 3,4
17: 8,5
18: 5,4
19: 5,4
20: 5,4
21: 2,3
22: 7,4
23: 8,5
24: 8,5
25: 4,4
26: 5,4
27: 4,3
28: 4,3
29: 1,2
30: 1,2
31: 1,3
32: 1,2
33: 4,4
34: 3,3
35: 6,4
36: 4,4
37: 3,3
38: 1,3
39: 5,4
40: 2,3
41: 3,3
42: 2,3
43: 4,3
44: 4,3
45: 4,3
46: 3,4

Game balance considerations and rationale
- Price clamp: 0.7–1.3 ensures that even with strong surpluses/shortages, prices won’t swing excessively. Our weights aim to generate surpluses in specialties and deficits in imports, but not extreme.
- BUY vs SELL spread: BUY multiplies by 0.75, SELL has ratio-based nudge; thus shops profit on spread and ratio incentives. Our weights maintain inventory flows so both buy and sell events occur (trade.c:586–608 for SELL, 532–584 for US virtual buy/consume).
- Industrial product: It’s special in update_trade_center, scaling with mineral_satisf. This ties industrial output to mineral imports or local mineral abundance. We set higher industrial product production in Industrial/Ind* and balanced in Commercial; low in Agricoltural and moderate in Mineral.
- Population bounds: 1,000–50,000 (trade.c:989–992). With weights in ~1..12, daily production/usage per commodity lands around 0.2..120 units before minimum clamp, which is reasonable.
- Overproduction cap: in_store capped to 5000 on save (trade.c:1059–1061). Our values are mindful but dynamic events can still push stores; the cap prevents runaway stockpiles.

How to write the .trd files
Each file has 94 lines. For commodity i in order 0..46, write:
- Line 2*i+1: Production weight (integer)
- Line 2*i+2: Consumption weight (integer)
Each line can include a trailing comma and comment string; the loader uses “%d,%*s”. Example lines (for Industrial):
1: 1, gold_P
2: 2, gold_C
3: 1, silver_P
4: 2, silver_C
...
93: 12, industrial_P
94: 6, industrial_C

Validation steps
- Ensure initialize_trade can parse the integers (no blank lines, each line begins with the integer).
- Start the MUD and verify: load_trade_center will compute production/usage as expected; CMD_LIST in trade center rooms will show prices and stock.
- Observe trade_center_random effects over time via NEWS (CMD_NEWS trade).

Appendix: Base item values (copper coins)
From comm_values in trade.c:
0 gold: 10000
1 silver: 1000
2 copper: 1000
3 platinum: 100000
4 iron: 100
5 bronze: 1000
6 adamantium: 5000
7 mithril: 15000
8 steel: 200
9 lead: 100
10 tin: 100
11 diamond: 100000
12 emerald: 50000
13 ruby: 40000
14 meat: 1000
15 salt: 2000
16 spices: 5000
17 milk: 400
18 ale: 1000
19 beer: 1000
20 wine: 2000
21 zzar: 10000
22 fruits: 400
23 wheat: 100
24 corn: 100
25 fish: 600
26 oil: 1200
27 sugar: 2000
28 honey: 1600
29 marble: 20
30 granite: 25
31 coal: 20
32 sulphur: 400
33 timber: 50
34 silk: 400
35 wool: 100
36 linen: 40
37 furs: 500
38 glass: 500
39 leather: 200
40 parchment: 1000
41 papyrus: 600
42 dye: 100
43 coffee: 10000
44 tea: 5000
45 tobacco: 20000
46 industrial product: 10000

Notable mechanics references
- Tables read: initialize_trade (trade.c:1066–1171)
- Selection by type: load_trade_center/update_trade_center (trade.c:922–951, 994–1025)
- Production/usage derivation: load_trade_center (trade.c:955–963) and update_trade_center (trade.c:1040–1047)
- Industrial product special: update_trade_center (trade.c:1045–1047)
- Price function: get_commodity_value (trade.c:415–455)
- Daily processes: production (tdPR), consumption/buy (tdUS), surplus sell (tdSELL)
- Random market events: trade_center_random (trade.c:1222–1338)
- Store cap: update_trade_center (trade.c:1059–1061)

Deliverable completeness checklist
1. All 46 commodities identified with names and indices. Yes.
2. 94-value mapping explained as P/C pairs per commodity (0..46). Yes; explicit indexing and special case outlined.
3. Appropriate economic values characterized for town types and encoded into concrete integer weights. Yes; detailed sets for 9 files.
4. Relationship files analyzed and tuned as hybrids. Yes.
5. Realistic integer values proposed for all 9 trade files, aligned with code mechanics and game balance. Yes.

Next steps to apply
- Replace each Players/Trade/*.trd content with the numeric lists above, one integer per line followed by a comma and a short comment, in the 0..46 order, P then C.
- Boot the game, ensure trade initialization succeeds (no log errors “Problem with trade file directory!” and prices displayed in trade centers).
- Observe over a few in-game days; adjust any outliers (e.g., if certain commodities never appear due to immediate consumption, increase P or reduce C for that type).
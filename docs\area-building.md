# Area Building Guide

How to create and maintain areas (zones) in Outcast MUD using OLC and file-based methods. This aligns with current systems and file formats used in the repository.

## Table of Contents

1. [Overview](#overview)
2. [Planning](#planning)
3. [OLC Basics](#olc-basics)
4. [Rooms](#rooms)
5. [Mobiles](#mobiles)
6. [Objects](#objects)
7. [Zones](#zones)
8. [Shops](#shops)
9. [Quests](#quests)
10. [Special Procedures](#special-procedures)
11. [Testing and Balance](#testing-and-balance)
12. [File Formats](#file-formats)

## Overview

Areas include Rooms, Mobiles (NPCs), Objects, Shops, Quests, and Zone reset data.
VNUMs are unique per entity type; coordinate blocks with admins to avoid conflicts.

## Planning

Before building, define:
- Theme, level range, size, standout features
- Story, key NPCs, quest arcs, world integration
- A simple map and navigation flow

Template:
```
Area: The Haunted Crypts
Author: YourName
VNUMs: Rooms 5000–5099, Mobs 5000–5049, Objs 5000–5099
Level Range: 15–25
Highlights: Day/night variation, access puzzle, cursed items
```

## OLC Basics

Enter OLC and manage edits:
```
olc
alist
aedit <zone#>

show
save
asave <area>
done
```

Safety:
- Save frequently; asave to persist.
- Build on a test port when possible.
- Keep backups of changed files.

## Rooms

Create/edit:
```
redit [vnum|create <vnum>]
```

Common fields:
```
name <title>
desc                    # opens editor
sector <type>
flags <flags>
exit <dir> <target_vnum> [door closed locked key <vnum>]
```

Sectors: inside, city, field, forest, hills, mountain, water_swim, water_noswim, underwater, flying

Flags: dark, death, nomob, indoors, peaceful, notrack, nomagic, tunnel, private, godroom

Guidance:
- 3–5 line descriptions, sensory detail, hint at exits.

## Mobiles

Create/edit:
```
medit [vnum|create <vnum>]
```

Fields:
```
name <keywords>
short <desc>
long <desc>
desc                    # long look
level <n>
align <-1000..1000>
hitroll <n>
damroll <n>
ac <n>
hitdice <XdY+Z>
damdice <XdY+Z>
```

Act flags: sentinel, scavenger, aggressive, stay_zone, wimpy, memory, helper, nocharm, nosummon, nosleep, nobash, noblind

Balance templates (examples):
- L1–5: HP 1d8+20..2d8+40, AC 90..70, dmg 1d4..1d6+1
- L10–15: HP 5d8+100..8d8+200, AC 50..30, dmg 2d4+2..2d6+4
- L20–30: HP 10d8+500..15d8+1000, AC 10..-20, dmg 3d6+8..4d6+12
- L40–50: HP 20d8+2000..30d8+5000, AC -40..-80, dmg 5d8+20..8d8+40

## Objects

Create/edit:
```
oedit [vnum|create <vnum>]
```

Fields:
```
name <keywords>
short <desc>
long <desc>
type <type>
wear <positions>
weight <lbs>
cost <gp>
```

Types: light, scroll, wand, staff, weapon, armor, potion, worn, other, trash, container, drinkcon, key, food, money, fountain

Values (per type):
- Weapon: type, ndice, sdice, flags
- Armor: ac, -, -, -
- Container: capacity, flags, key vnum, -

Flags: glow, hum, norent, nodonate, noinvis, invisible, magic, nodrop, bless, anti_good/evil/neutral, noremove

Balance:
- Weapons up to roughly +8 hit/dam in normal ranges
- Armor AC typical -1..-20 by level bands
- Stat bonuses generally +1..+4

## Zones

Control loading/resets with ZEDIT:
```
zedit <zone#>
show
new/delete
```

Commands:
- M: load mobile
- O: load object
- G: give obj to last mob
- E: equip obj on last mob
- D: door state (0 open, 1 closed, 2 locked)
- P: put obj in container

Reset cadence with reset/lifespan minutes.

## Shops

Define produce lists, buy/sell multipliers, accepted item types, messages, keeper, location, hours. See example in this file. Place shop files under areas/shp and link from zone/world configs as required by your distribution.

## Quests

Implement via spec procs or quest systems. Track flags to prevent repetition and support branching. Keep rewards aligned with area level.

Example spec proc and assignment shown below.

## Special Procedures

Implement behaviors in specs.*.c and assign in specs.assign.c:
```c
SPECIAL(guardian) { /* ... */ }
ASSIGNMOB(5001, quest_giver);
ASSIGNOBJ(5100, healing_fountain);
ASSIGNROOM(5050, guardian_room);
```

## Testing and Balance

Checklist:
- Bidirectional exits, no traps
- Mob difficulty, exp/gold balance
- Item stats sane, no outliers
- Quests completable, no exploits

Guidelines:
- XP ≈ L*L*10..20
- Gold ≈ L*10..50
- Rarity spread: few rares, some uncommons, many commons

## File Formats

Quick references for .wld, .mob, .obj appear below for convenience. For deeper details, see docs/area-building.md sections above and compare with existing data in areas/*.

This guide reflects the current codebase conventions and file layout. If core loaders or formats change, update examples accordingly.
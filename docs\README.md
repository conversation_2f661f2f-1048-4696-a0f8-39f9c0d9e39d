# Outcast MUD Documentation

Welcome to the official documentation for Outcast MUD, a multi-user dungeon (MUD) set in the Forgotten Realms–inspired universe.

## Table of Contents

1. [Overview](#overview)
2. [Getting Started](getting-started.md)
3. [Installation Guide](installation.md)
4. [Player Guide](player-guide.md)
5. [Game Mechanics](game-mechanics.md)
6. [Admin/Wizard Documentation](admin-guide.md)
7. [Developer Documentation](developer-guide.md)
8. [Area Building](area-building.md)
9. [Network Protocol](network-protocol.md)

## Overview

Outcast MUD is a text-based multiplayer role-playing game derived from the DikuMUD/CircleMUD lineage and extensively customized. Key capabilities include:

- Rich fantasy world with numerous areas (e.g., Waterdeep, Baldur’s Gate, Calimshan, the Underdark)
- Multiple races and classes with distinct abilities
- Optional PvP with rules configured server-side
- Player associations (guilds and organizations)
- Questing across the realms
- Economy with shops and trading
- Player housing

## Project Structure

```
Outcast-MUD/
├── src/              # C source code and Makefile (builds 'ocm_new')
├── areas/            # World data
│   ├── wld/          # Rooms
│   ├── mob/          # Mobiles (NPCs)
│   ├── obj/          # Objects
│   ├── zon/          # Zones
│   ├── shp/          # Shops
│   └── qst/          # Quests
├── lib/              # Runtime data and configs
│   ├── boards/       # In-game message boards
│   ├── logs/         # Boot/errors/status/debug logs
│   ├── misc/         # Various configuration
│   └── information/  # Help files and MOTDs
├── Players/          # Player files and mail
├── Mobiles/          # Mobile-specific data
└── docs/             # Documentation
```

Notes:
- The server binary built by the Makefile is named `ocm_new`. References to `outcast` in scripts should be validated or updated accordingly.
- Default compression support uses zlib (`-lz` linked in the Makefile). MySQL is not linked by default.

## Key Features

### World and Travel
- Time and weather systems
- Wilderness exploration grids
- Ships and sea travel
- Planar and inter-area travel hooks

### Character System
- Many playable races (e.g., Human, Elf, Dwarf, Drow)
- Multiple classes (e.g., Warrior, Mage, Cleric, Thief, plus specializations)
- Skill and spell progression
- Alignment system influencing gameplay
- Spell memorization mechanics for casters

### Social and Systems
- Associations (guilds)
- In-game mail and boards
- Player-run economy elements
- Contract/quest systems

## Quick Links

- [New Player Guide](getting-started.md)
- [Command Reference](player-guide.md)
- [Building Tutorial](area-building.md)
- [Code Architecture](developer-guide.md)

## Legal

Copyright (c) 2000, Kristopher Scott Kortright.
All rights reserved. This code is not freeware or shareware and may not be used without express written permission from the author.

---
For details, refer to the specific documentation sections linked above.
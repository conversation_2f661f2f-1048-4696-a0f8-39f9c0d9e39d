# Installation Guide

This guide covers installing and running your own Outcast MUD server.

## Table of Contents

1. [System Requirements](#system-requirements)
2. [Prerequisites](#prerequisites)
3. [Installation Steps](#installation-steps)
4. [Configuration](#configuration)
5. [Starting the Server](#starting-the-server)
6. [Post-Installation](#post-installation)
7. [Troubleshooting](#troubleshooting)
8. [Maintenance](#maintenance)

## System Requirements

### Minimum

- OS: Linux/Unix (Ubuntu, Debian, CentOS, etc.)
- RAM: 512MB (1GB+ recommended)
- Storage: 500MB base (1GB+ with player data)
- CPU: 1 core (2+ recommended)
- Network: Static IP or dynamic DNS

### Recommended

- OS: Ubuntu 20.04 LTS or newer
- RAM: 2GB+
- Storage: 5GB+ (logs/backups)
- CPU: 2+ cores
- Network: Reliable bandwidth

## Prerequisites

### Required Software

```bash
# Ubuntu/Debian
sudo apt-get update
sudo apt-get install -y build-essential gcc make zlib1g-dev

# Optional but recommended
sudo apt-get install -y gdb valgrind screen tmux git
```

Notes:
- The current build links zlib only (-lz). MySQL client is not used by default.

### MySQL (Optional)

MySQL integration is disabled by default. The server uses file-based persistence. To experiment with MySQL, you must modify code and the Makefile to link libmysqlclient and enable related modules.

```bash
# Only if enabling MySQL support
# sudo apt-get install -y mysql-server mysql-client libmysqlclient-dev
# sudo mysql_secure_installation
# mysql -u root -p
# CREATE DATABASE outcast_mud;
# CREATE USER 'outcast'@'localhost' IDENTIFIED BY 'strong_password';
# GRANT ALL PRIVILEGES ON outcast_mud.* TO 'outcast'@'localhost';
# FLUSH PRIVILEGES;
```

## Installation Steps

### 1) Get the Source

```bash
# Create directory
mkdir -p /home/<USER>/outcast && cd /home/<USER>/outcast

# Option A: from archive
# tar -xzvf outcast-mud.tar.gz

# Option B: from repository
git clone <repository-url> .
```

### 2) Configure (Optional)

Defaults work out of the box. You can adjust compile-time settings in `src/config.h` if present (port, limits, paths). Paths typically resolve relative to the MUD root:

```c
/* Examples (actual values use #define macros in headers) */
#define LIB_PATH     "../lib/"
#define LOG_PATH     "../lib/logs/"
#define PLAYER_PATH  "../Players/"
/* Default port often defined in headers (e.g., 4000) */
```

### 3) Build

```bash
cd src
make clean
make

# Output binary name (per Makefile):
ls -la ocm_new
```

Important:
- The Makefile builds `ocm_new` (not `outcast`). Update any scripts accordingly.

### 4) Permissions

```bash
# From MUD root
chmod -R 755 lib areas
chmod -R 755 lib/boards
mkdir -p lib/logs Players
chmod -R 775 lib/logs Players
```

Rationale:
- The server writes logs to `lib/logs` and player files to `Players`.

## Configuration

### Area Links

Verify world links (varies by distribution). Example:

```bash
cd areas
ls -la world.*
# Expect symlinks such as:
# world.mob -> AREA.mobobj
# world.obj -> AREA.mobobj
# world.qst -> QUEST
# world.shp -> SHOP
# world.wld -> AREA
# world.zon -> AREA
```

### Port and Run Script

If you use a helper script, ensure it starts `ocm_new` and logs to `lib/logs/boot.log`. Example `run.sh`:

```bash
#!/bin/bash
set -euo pipefail
PORT="${1:-4000}"

cd "$(dirname "$0")"

if [ -f .outcast.pid ] && kill -0 "$(cat .outcast.pid)" 2>/dev/null; then
  echo "MUD already running (PID $(cat .outcast.pid))."
  exit 1
fi

./src/ocm_new "$PORT" > lib/logs/boot.log 2>&1 &
echo $! > .outcast.pid
echo "MUD started on port $PORT (PID: $(cat .outcast.pid))"
```

Make it executable:

```bash
chmod +x run.sh
```

### Initial Admin

Create an implementor by creating a character then promoting via in-game immortal commands (if available) or by editing the player file directly. Process varies per admin policy.

## Starting the Server

### Manual

```bash
cd /home/<USER>/outcast
./src/ocm_new 4000 &
```

### Script

```bash
cd /home/<USER>/outcast
./run.sh 4000
```

### Screen/Tmux

```bash
# screen
screen -S outcast
cd /home/<USER>/outcast && ./src/ocm_new 4000
# Detach: Ctrl+A, D  |  Reattach: screen -r outcast

# tmux
tmux new -s outcast
cd /home/<USER>/outcast && ./src/ocm_new 4000
# Detach: Ctrl+B, D  |  Reattach: tmux attach -t outcast
```

### systemd (Recommended)

`/etc/systemd/system/outcast-mud.service`:

```ini
[Unit]
Description=Outcast MUD Server
After=network.target

[Service]
Type=simple
User=mud
WorkingDirectory=/home/<USER>/outcast
ExecStart=/home/<USER>/outcast/src/ocm_new 4000
Restart=on-failure
RestartSec=30

[Install]
WantedBy=multi-user.target
```

Enable/start:

```bash
sudo systemctl enable outcast-mud
sudo systemctl start outcast-mud
sudo systemctl status outcast-mud
```

## Post-Installation

### Connect and Test

```bash
telnet localhost 4000
# Expect the greeting screen
```

### Firewall

```bash
sudo ufw allow 4000/tcp
# or iptables:
sudo iptables -A INPUT -p tcp --dport 4000 -j ACCEPT
```

### Backups

`/home/<USER>/backup.sh`:

```bash
#!/bin/bash
set -euo pipefail
BACKUP_DIR="/home/<USER>/backups"
DATE="$(date +%Y%m%d_%H%M%S)"
mkdir -p "$BACKUP_DIR"

tar -czf "$BACKUP_DIR/players_$DATE.tar.gz" Players/
tar -czf "$BACKUP_DIR/areas_$DATE.tar.gz" areas/

find "$BACKUP_DIR" -name "*.gz" -mtime +30 -delete
echo "Backup completed: $DATE"
```

Crontab:

```bash
0 3 * * * /home/<USER>/backup.sh >> /home/<USER>/backups/backup.log 2>&1
```

## Troubleshooting

### Ports

```bash
sudo ss -tlnp | grep 4000 || sudo netstat -tlnp | grep 4000
# Kill if necessary:
# sudo kill -9 <PID>
```

### Build Issues

```bash
# Missing zlib headers
sudo apt-get install -y zlib1g-dev

# Clean rebuild
make clean && make
```

### Permissions

```bash
chmod -R 755 /home/<USER>/outcast
chmod -R 775 lib/logs Players
```

### Logs

- lib/logs/boot.log
- lib/logs/errors
- lib/logs/debug
- lib/logs/status

## Maintenance

### Cadence

- Daily: Check errors, player count, backups
- Weekly: Resource review, unusual activity, area updates
- Monthly: Restore-test backups, rotate logs, review docs

### Monitoring

Simple monitor (optional):

```bash
#!/bin/bash
while true; do
  if ! pgrep -f "ocm_new 4000" >/dev/null; then
    echo "$(date): MUD not running; restarting..." >> restart.log
    cd /home/<USER>/outcast && ./run.sh 4000
  fi
  sleep 60
done
```

## Security

- Keep OS/packages updated
- Restrict firewall to necessary ports
- Limit SSH access and enforce strong passwords
- Monitor logs for suspicious activity

## Advanced

### Multiple Ports

```bash
./src/ocm_new 4000 &
./src/ocm_new 4001 &
```

Note: Running multiple instances may require separate runtime directories to avoid file contention (e.g., logs/Players).

### Development Instance

```bash
cp -r /home/<USER>/outcast /home/<USER>/outcast-dev
# Use different port, separate Players/logs dirs, and configs
```

This guide reflects the current build system (Makefile builds ocm_new and links zlib). Adjust instructions if you change build outputs or dependencies.
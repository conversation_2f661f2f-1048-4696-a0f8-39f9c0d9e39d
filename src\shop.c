/* ***************************************************************************
 *  File: shop.c                                               Part of Outcast *
 *  Usage: Procedures handling shops and shopkeepers.                        *
 *  Copyright  1990, 1991 - see 'license.doc' for complete information.      *
 *  Copyright  1994, 1995 - Outcast Systems Ltd.                             *
 *************************************************************************** */

/* *************************************************************************
 *                                                                         *
 *    The new portions of this code are Copyright(c) 1994 by <PERSON><PERSON><PERSON>    *
 <PERSON>   <PERSON>, and are authorized for use ONLY on Outcast Dikumud   *
 *  This code may not be used without Expressly Written Permission by the  *
 *   author, unless in conjunction with Outcast Diku.  K. Kortright may    *
 *         be reached via <NAME_EMAIL> or          *
 *                       <EMAIL>                       *
 *                                                                         *
 ************************************************************************* */

#include <ctype.h>
#include <stdio.h>
#include <string.h>

#include "comm.h"
#include "db.h"
#include "interp.h"
#ifdef NEWJUSTICE
   #include "newjustice.h"
#endif
#ifdef OLDJUSTICE
   #include "justice.h"
#endif
#include "prototypes.h"
#include "race_class.h"
#include "specs.include.h"
#include "specs.prototypes.h"
#include "structs.h"
#include "utils.h"

/* external variables */

extern P_index mob_index;
extern P_obj obj_index;
extern P_room world;
extern char *drinknames[];
extern const struct stat_data stat_factor[];
extern struct cha_app_type cha_app[];
extern struct str_app_type str_app[];
extern struct time_info_data time_info;
extern const struct race_lookup shop_bigot_table[];
extern struct zone_data *zone_table;

static const struct
   {
   int idx;
   const char *keyword;
   } shop_keys[] = {
   {SHOP_KEY_BT,       "BT"},
   {SHOP_KEY_CASTING,  "CASTING"},
   {SHOP_KEY_CHEATS,   "CHEATS"},
   {SHOP_KEY_DEADBEAT, "DEADBEAT"},
   {SHOP_KEY_GREED,    "GREED"},
   {SHOP_KEY_HATES,    "HATES"},
   {SHOP_KEY_HOURS,    "HOURS"},
   {SHOP_KEY_KILLABLE, "KILLABLE"},
   {SHOP_KEY_MBCASH,   "MBCASH"},
   {SHOP_KEY_MBHAVE,   "MBHAVE"},
   {SHOP_KEY_MBIGOT,   "MBIGOT"},
   {SHOP_KEY_MBUY,     "MBUY"},
   {SHOP_KEY_MCLOSE,   "MCLOSE"},
   {SHOP_KEY_MNBUY,    "MNBUY"},
   {SHOP_KEY_MOPEN,    "MOPEN"},
   {SHOP_KEY_MSCASH,   "MSCASH"},
   {SHOP_KEY_MSELL,    "MSELL"},
   {SHOP_KEY_MSHAVE,   "MSHAVE"},
   {SHOP_KEY_OFFENSE,  "OFFENSE"},
   {SHOP_KEY_PO,       "PO"},
   {SHOP_KEY_PROFIT,   "PROFIT"},
   {SHOP_KEY_ROAMING,  "ROAMING"},
   {SHOP_KEY_ROOM,     "ROOM"},
   {SHOP_KEY_SHOP,     "SHOP"}
};

struct shop_data *shop_index;
int number_of_shops = 0;

/* returns:
 * 0 - keeper deals normally with ch
 * 1 - keeper cheats ch
 * 2 - keeper hates ch (and won't deal with him/her)
 */

int shop_reaction(P_char keeper, P_char ch, int shop_nr)
{

   int check_bigot = 0;

   /* we scan hates first, and either drop through ot return 2, then scan cheats and either drop through or return 1
    * the default is return 0, since we couldn't find a match.
    */

   /* check the degenerate cases first */
   if(IS_CSET(shop_index[shop_nr].hates, BIGOT_ALL))
      return 2;
   if(IS_CSET(shop_index[shop_nr].hates, BIGOT_NPC) && IS_NPC(ch))
      return 2;
   if(IS_CSET(shop_index[shop_nr].hates, BIGOT_ALIEN) && (GET_RACE(ch) != GET_RACE(keeper)))
      return 2;
   if(IS_CSET(shop_index[shop_nr].hates, BIGOT_OWN) && (GET_RACE(ch) == GET_RACE(keeper)))
      return 2;
   if(IS_CSET(shop_index[shop_nr].hates, BIGOT_EVILS) && RACE_EVIL(ch))
      return 2;
   if(IS_CSET(shop_index[shop_nr].hates, BIGOT_GOODS) && RACE_GOOD(ch))
      return 2;

   /* by one of those strange quirks, BIGOT_HUMAN and RACE_HUMAN are the same number, weird huh? */
   if((GET_RACE(ch) <= LAST_PC_RACE) && IS_CSET(shop_index[shop_nr].hates, GET_RACE(ch)))
      return 2;

   /* this is not consistent, can't rely on it - Iyachtu */
#if 0
   /* by another one of those strange quirks, BIGOT_WARRIOR and CLASS_WARRIOR are exactly 13 apart, dodododo */
   if(GET_CLASS(ch) != CLASS_ROGUE)
      {
      if(IS_PC(ch) && IS_CSET(shop_index[shop_nr].hates, (GET_CLASS(ch) + 13)))
         return 2;
      }
   else if(IS_PC(ch) && IS_CSET(shop_index[shop_nr].hates, CLASS_THIEF))
      return 2;
#endif

   switch(GET_CLASS(ch))
      {
      case CLASS_WARRIOR:
         check_bigot = BIGOT_WARRIOR;
         break;
      case CLASS_RANGER:
         check_bigot = BIGOT_RANGER;
         break;
      case CLASS_BERSERKER:
         check_bigot = BIGOT_BERSERKER;
         break;
      case CLASS_PALADIN:
         check_bigot = BIGOT_PALADIN;
         break;
      case CLASS_ANTIPALADIN:
         check_bigot = BIGOT_ANTIPALADIN;
         break;
      case CLASS_CLERIC:
         check_bigot = BIGOT_CLERIC;
         break;
      case CLASS_MONK:
         check_bigot = BIGOT_MONK;
         break;
      case CLASS_DRUID:
         check_bigot = BIGOT_DRUID;
         break;
      case CLASS_SHAMAN:
         check_bigot = BIGOT_SHAMAN;
         break;
      case CLASS_SORCERER:
         check_bigot = BIGOT_SORCERER;
         break;
      case CLASS_NECROMANCER:
         check_bigot = BIGOT_NECROMANCER;
         break;
      case CLASS_CONJURER:
         check_bigot = BIGOT_CONJURER;
         break;
      case CLASS_THIEF:
      case CLASS_ROGUE:
         check_bigot = BIGOT_THIEF;
         break;
      case CLASS_ASSASSIN:
         check_bigot = BIGOT_ASSASSIN;
         break;
      case CLASS_MERCENARY:
         check_bigot = BIGOT_MERCENARY;
         break;
      case CLASS_BARD:
      case CLASS_BATTLECHANTER:
         check_bigot = BIGOT_BARD;
         break;
      case CLASS_PSIONICIST:
         check_bigot = BIGOT_PSIONICIST;
         break;
      case CLASS_LICH:
         check_bigot = BIGOT_LICHCLASS;
         break;
      case CLASS_ENCHANTER:
         check_bigot = BIGOT_ENCHANTER;
         break;
      case CLASS_INVOKER:
         check_bigot = BIGOT_INVOKER;
         break;
      case CLASS_ILLUSIONIST:
         check_bigot = BIGOT_ILLUSIONIST;
         break;
      case CLASS_ELEMENTALIST:
         check_bigot = BIGOT_ELEMENTALIST;
         break;
      case CLASS_DIRERAIDER:
         check_bigot = BIGOT_DIRERAIDER;
         break;
      default:
         wizlog(51, "Problem with shop: class of %s not recognized!", GET_NAME(ch));
         return 2;
      }

   if(IS_PC(ch) && IS_CSET(shop_index[shop_nr].hates, check_bigot))
      return 2;


   /* ok, now the same checks for cheats */

   /* check the degenerate cases first */
   if(IS_CSET(shop_index[shop_nr].cheats, BIGOT_ALL))
      return 1;
   if(IS_CSET(shop_index[shop_nr].cheats, BIGOT_NPC) && IS_NPC(ch))
      return 1;
   if(IS_CSET(shop_index[shop_nr].cheats, BIGOT_ALIEN) && (GET_RACE(ch) != GET_RACE(keeper)))
      return 1;
   if(IS_CSET(shop_index[shop_nr].cheats, BIGOT_OWN) && (GET_RACE(ch) == GET_RACE(keeper)))
      return 1;
   if(IS_CSET(shop_index[shop_nr].cheats, BIGOT_EVILS) && RACE_EVIL(ch))
      return 1;
   if(IS_CSET(shop_index[shop_nr].cheats, BIGOT_GOODS) && RACE_GOOD(ch))
      return 1;

   if((GET_RACE(ch) <= LAST_PC_RACE) && IS_CSET(shop_index[shop_nr].cheats, GET_RACE(ch)))
      return 1;
   if(IS_PC(ch) && IS_CSET(shop_index[shop_nr].cheats, (GET_CLASS(ch) + 13)))
      return 1;

   return 0;
}

/* passes along the return from shop_reaction (or 2 if the shop won't deal for any reason) */

int is_ok(P_char keeper, P_char ch, int shop_nr)
{
   char Gbuf1[MAX_STRING_LENGTH];
   int i, j, reaction, crimes = 0;
   crm_rec *crec = NULL;

   reaction = shop_reaction(keeper, ch, shop_nr);

   /* If shopkeeper is bigoted, turn customer away. MIAX */
   if(reaction == 2)
      {  /* hates */
      act(shop_index[shop_nr].SM_hates, FALSE, keeper, 0, ch, TO_ROOM);
      return 2;
      }

   /* ok, to allow for shops open at night, we need to check the ranges of times, not just the limits */

   if(shop_index[shop_nr].open)
      {
      /* shop is open */

      if(!(CAN_SEE(keeper, ch)) && !IS_TRUSTED(ch))
         {
         mobsay(keeper, "I don't trade with someone I can't see!");
         return 2;
         }

#if 0 // temp turned off to stop crashes -Azuth 8/16/2003
      /* penalties for bad or wanted criminals - shops won't deal with them */
      while((crec = crime_find(hometowns[CHAR_IN_TOWN(ch)-1].crime_list,
                               GET_NAME(ch), NULL, 0, NOWHERE, J_STATUS_NONE, crec)))
         {
         switch(crec->status)
            {
            case J_STATUS_WANTED:
            case J_STATUS_DEBT:
            case J_STATUS_CRIME:
               crimes += 1;
               break;
            default:
               continue;
               break;
            }
         }

      if(crimes > 5)
         {
         mobsay(keeper, "I don't deal with wanted criminals!");
         return 2;
         }
#endif // temp turned off to stop crashes -Azuth 8/16/2003

      }
   else
      {
      /* shop is closed */

      /* find next open hour */
      j = time_info.hour;
      for(i = 0; i < 24; i++)
         {
         if(j > 23)
            j = 0;
         if(IS_CSET(shop_index[shop_nr].hours, j))
            break;
         j++;
         }
      sprintf(Gbuf1, "We are closed, we'll reopen at %d%s, come back then.\n",
              (j % 12) ? (j % 12) : 12,
              (j == 12) ? " noon" : (j == 0) ? " midnight" : (j > 11) ? "pm" : "am");
      mobsay(keeper, Gbuf1);
      return 2;
      }

   return reaction;
}

int shop_will_buy(int shop_nr, P_obj item)
{
   if(item->cost < 1)
      return FALSE;

   if(IS_SET(item->extra_flags, ITEM_NOSELL))
      return FALSE;

   if(IS_SET(item->extra_flags, ITEM_NORENT))
      return FALSE;

   if(IS_SET(item->extra_flags, ITEM_NODROP))
      return FALSE;

   if(IS_SET(item->extra_flags, ITEM_TRANSIENT))
      return FALSE;

   if(!IS_CSET(shop_index[shop_nr].types, ((int)(item->type))))
      return FALSE;

   return TRUE;
}

int shop_producing(P_obj item, int shop_nr)
{
   int counter;

   if(item->R_num < 0)
      return FALSE;

   for(counter = 0; counter < shop_index[shop_nr].production_count; counter++)
      if(shop_index[shop_nr].production[counter] == item->R_num)
         return TRUE;

   return FALSE;
}

void shopping_buy(char *arg, P_char ch, P_char keeper, int shop_nr, int qty, P_obj where_to)
{
   P_obj temp1;
   char Gbuf1[MAX_STRING_LENGTH], argm[MAX_INPUT_LENGTH];
   int i = 0, sale, reaction, cost_mod;

   if((reaction = is_ok(keeper, ch, shop_nr)) == 2)
      return;

   one_argument(arg, argm);
   if(!*argm)
      {
      sprintf(Gbuf1, "WHAT do you want to buy %s?  Try 'list'", C_NAME(ch));
      mobsay(keeper, Gbuf1);
      return;
      }

   /* Added if statement from epic code from buying by numbers MIAX */
   temp1 = get_obj_in_list_vis(ch, argm, keeper->carrying);
   if(!temp1)
      {
      if(atoi(argm))
         {
         for(temp1 = keeper->carrying; temp1; temp1 = temp1->next_content)
            {
            if(CAN_SEE_OBJ(ch, temp1) && CAN_SEE_OBJ(keeper, temp1) && (temp1->cost > 0))
               {
               if(++i == atoi(argm))
                  break;
               }
            }
         }

      if(!temp1)
         {
         sprintf(Gbuf1, shop_index[shop_nr].SM_shop_not_have, argm);
         act(Gbuf1, FALSE, keeper, 0, ch, TO_ROOM);
         return;
         }
      }

   if(temp1->cost <= 0)
      {
      sprintf(Gbuf1, shop_index[shop_nr].SM_shop_not_have, argm);
      act(Gbuf1, FALSE, keeper, 0, ch, TO_ROOM);
      extract_obj(temp1);
      return;
      }

   cost_mod = cha_app[STAT_INDEX(GET_C_CHA(ch))].modifier;
   if(GET_RACE(ch) != GET_RACE(keeper))
      cost_mod = cost_mod / 2;

   cost_mod = shop_index[shop_nr].greed * (100 - cost_mod) / 100;

   sale = (temp1->cost * cost_mod) / 100;

   if(reaction == 1)
      sale *= 2;

   sale *= qty; // how many they wanna buy?

   if((GET_MONEY(ch) < sale) && !IS_TRUSTED(ch))
      {
      act(shop_index[shop_nr].SM_buyer_broke, FALSE, keeper, temp1, ch, TO_ROOM);
      return;
      }

   if(!where_to && ((IS_CARRYING_N(ch) + qty) > CAN_CARRY_N(ch)))
      {
      sprintf(Gbuf1, "You are carrying too many items %s.", C_NAME(ch));
      mobsay(keeper, Gbuf1);
      return;
      }

   if(where_to)
      {
      if(where_to->type != ITEM_CONTAINER)
         {
         sprintf(Gbuf1, "Sorry %s, but I can't load anything into your %s.", C_NAME(ch), where_to->short_description);
         mobsay(keeper, Gbuf1);
         return;
         }

      if(IS_SET(where_to->value[1], CONT_CLOSED))
         {
         sprintf(Gbuf1, "Your %s seems to be closed %s.", where_to->short_description, C_NAME(ch));
         mobsay(keeper, Gbuf1);
         return;
         }

      if((where_to->weight + (qty * temp1->weight)) > where_to->value[0])
         {
         sprintf(Gbuf1, "Your %s won't hold that many %s, maybe buy a few less.", where_to->short_description, C_NAME(ch));
         mobsay(keeper, Gbuf1);
         return;
         }
      }

   /* Test if producing shop! */
   if(!shop_producing(temp1, shop_nr) && qty != 1)
      {
      sprintf(Gbuf1, "I'm afraid you'll have to purchase those one at a time %s.", C_NAME(ch));
      mobsay(keeper, Gbuf1);
      return;
      }

   if(!IS_TRUSTED(ch))
      { /* pay keeper */
      if(SUB_MONEY(ch, sale, 0) < 0)
         return;
      }

   /* Test if producing shop! */
   if(!shop_producing(temp1, shop_nr))
      {
      obj_from_char(temp1);
      if(where_to)
         obj_to_obj(temp1, where_to);
      else
         obj_to_char(temp1, ch);
      }
   else
      {
      i = qty;
      while(i)
         {
         temp1 = clone_obj(temp1);
         if(where_to)
            obj_to_obj(temp1, where_to);
         else
            obj_to_char(temp1, ch);
         i--;
         }
      }

   sprintf(Gbuf1, shop_index[shop_nr].SM_sell_to,  coin_stringv(sale));
   act(Gbuf1, FALSE, keeper, temp1, ch, TO_ROOM);

   // nothing fancy on plurals for now, try handling boxes correctly for fun!
   if(qty > 1)
      {
      act("$n sells $p(s) to $N.", 1, keeper, temp1, ch, TO_NOTVICT);
      act("You receive $p(s) from $n.", 0, keeper, temp1, ch, TO_VICT);
      act("You sell $p(s) to $N.", 0, keeper, temp1, ch, TO_CHAR);
      }
   else
      {
      act("$n sells $p to $N.", 1, keeper, temp1, ch, TO_NOTVICT);
      act("You receive $p from $n.", 0, keeper, temp1, ch, TO_VICT);
      act("You sell $p to $N.", 0, keeper, temp1, ch, TO_CHAR);
      }

   return;
}

void shopping_sell(char *arg, P_char ch, P_char keeper, int shop_nr)
{
   P_obj temp1;
   char Gbuf1[MAX_STRING_LENGTH], argm[MAX_INPUT_LENGTH];
   int sale, reaction, cost_mod;

   if((reaction = is_ok(keeper, ch, shop_nr)) == 2)
      return;

   one_argument(arg, argm);

   if(!(*argm))
      {
      sprintf(Gbuf1, "WHAT do you want to sell %s?", C_NAME(ch));
      mobsay(keeper, Gbuf1);
      return;
      }
   if(!(temp1 = get_obj_in_list_vis(ch, argm, ch->carrying)))
      {
      act(shop_index[shop_nr].SM_buyer_not_have, FALSE, keeper, 0, ch, TO_ROOM);
      return;
      }

   /* patch to avoid selling containers full of stuff.  JAB */
   if((temp1->type == ITEM_CONTAINER) && temp1->contains)
      {
      sprintf(Gbuf1, "HA!  I'm not buying that,  %s.  Who knows what might be in there!", C_NAME(ch));
      mobsay(keeper, Gbuf1);
      return;
      }

   if(IS_CSET(temp1->sets_affs, AFF_GROUP_CACHED))
      {
      send_to_char("That item is still in your group cache, you must remove it first.", ch);
      return;
      }

   cost_mod = cha_app[STAT_INDEX(GET_C_CHA(ch))].modifier;
   if(GET_RACE(ch) != GET_RACE(keeper))
      cost_mod = cost_mod / 2;

   cost_mod = (shop_index[shop_nr].greed * 100) / (100 + shop_index[shop_nr].profit) * (100 + cost_mod) / 100;

   sale = (temp1->cost * cost_mod) / 100;

   if(reaction == 1)
      sale /= 2;

   if((sale < 1) || !shop_will_buy(shop_nr, temp1))
      {
      act(shop_index[shop_nr].SM_not_buy, FALSE, keeper, temp1, ch, TO_ROOM);
      return;
      }
   if((shop_index[shop_nr].shop_is_roaming == 1) && (GET_MONEY(keeper) < sale))
      {
      act(shop_index[shop_nr].SM_shop_broke, FALSE, keeper, temp1, ch, TO_ROOM);
      return;
      }

   ADD_MONEY(ch, sale);

   if(shop_index[shop_nr].shop_is_roaming == 1)
      SUB_MONEY(keeper, sale, 0);

   sprintf(Gbuf1, shop_index[shop_nr].SM_buy_from, coin_stringv(sale));
   act(Gbuf1, FALSE, keeper, temp1, ch, TO_ROOM);

   obj_from_char(temp1);

   act("$N sells $p to $n.", 1, keeper, temp1, ch, TO_NOTVICT);
   act("You receive $p from $N.", 0, keeper, temp1, ch, TO_CHAR);
   act("You sell $p to $n.", 0, keeper, temp1, ch, TO_VICT);

   if(get_obj_in_list_num(temp1->R_num, keeper->carrying))
      {
      extract_obj(temp1);
      temp1 = NULL;
      }
   else
      {
      obj_to_char(temp1, keeper);
      }

   return;
}

void shopping_value(char *arg, P_char ch, P_char keeper, int shop_nr)
{
   P_obj temp1;
   char Gbuf1[MAX_STRING_LENGTH], argm[MAX_INPUT_LENGTH];
   int sale, reaction, cost_mod;

   if((reaction = is_ok(keeper, ch, shop_nr)) == 2)
      return;

   one_argument(arg, argm);

   if(!(*argm))
      {
      sprintf(Gbuf1, "What do you want me to valuate %s?", C_NAME(ch));
      mobsay(keeper, Gbuf1);
      return;
      }
   if(!(temp1 = get_obj_in_list_vis(ch, argm, ch->carrying)))
      {
      act(shop_index[shop_nr].SM_shop_not_have, FALSE, keeper, 0, ch, TO_ROOM);
      return;
      }
   if(!shop_will_buy(shop_nr, temp1))
      {
      act(shop_index[shop_nr].SM_not_buy, FALSE, keeper, temp1, ch, TO_ROOM);
      return;
      }

   cost_mod = cha_app[STAT_INDEX(GET_C_CHA(ch))].modifier;
   if(GET_RACE(ch) != GET_RACE(keeper))
      cost_mod = cost_mod / 2;

   cost_mod = (shop_index[shop_nr].greed * 100) / (100 + shop_index[shop_nr].profit) * (100 + cost_mod) / 100;

   sale = (temp1->cost * cost_mod) / 100;

   if(reaction == 1)
      sale /= 2;

   if(sale < 1)
      {
      sprintf(Gbuf1, "HA! You couldn't pay me to take that!");
      mobsay(keeper, Gbuf1);
      return;
      }

   sprintf(Gbuf1, "I'll give you %s for %s, %s!", coin_stringv(sale), temp1->short_description, C_NAME(ch));
   mobsay(keeper, Gbuf1);

   return;
}

void shopping_list(char *arg, P_char ch, P_char keeper, int shop_nr)
{
   P_obj temp1;
   char Gbuf1[MAX_STRING_LENGTH], Gbuf2[MAX_STRING_LENGTH];
   char Gbuf3[MAX_STRING_LENGTH], Gbuf4[MAX_STRING_LENGTH];
   int found_obj, temp, sale, cost_mod, reaction;

   if(!ch->desc || (reaction = is_ok(keeper, ch, shop_nr)) == 2)
      return;

   cost_mod = cha_app[STAT_INDEX(GET_C_CHA(ch))].modifier;
   if(GET_RACE(ch) != GET_RACE(keeper))
      cost_mod = cost_mod / 2;

   cost_mod = shop_index[shop_nr].greed * (100 - cost_mod) / 100;

   /* New routine for listing goods by numbers. MIAX */
   temp = 0;
   strcpy(Gbuf1, "You can buy:\n");
   found_obj = FALSE;
   if(keeper->carrying)
      for(temp1 = keeper->carrying; temp1; temp1 = temp1->next_content)
         if(CAN_SEE_OBJ(ch, temp1) && CAN_SEE_OBJ(keeper, temp1) && (temp1->cost > 0))
            {
            temp++;
            found_obj = TRUE;

            sale = (temp1->cost * cost_mod) / 100;

            if(reaction == 1)
               sale *= 2;

            if(temp1->type != ITEM_DRINKCON)
               {
               sprintf(Gbuf2, "%s for %s. %s\n", (temp1->short_description), coin_stringv(sale),
                       (!can_char_use_item(ch, temp1) ? "(*)" : ""));

               }
            else
               {
               if(temp1->value[1])
                  sprintf(Gbuf3, "%s of %s", (temp1->short_description), drinknames[temp1->value[2]]);
               else
                  sprintf(Gbuf3, "%s", (temp1->short_description) ? temp1->short_description : "");
               sprintf(Gbuf2, "%s for %s.\n", Gbuf3, coin_stringv(sale));
               }
            if(temp < 10)
               sprintf(Gbuf4, " %d) ", temp);
            else
               sprintf(Gbuf4, "%d) ", temp);
            CAP(Gbuf2);
            strcat(Gbuf4, Gbuf2);
            strcat(Gbuf1, Gbuf4);
            };

   if(!found_obj)
      strcat(Gbuf1, "Nothing!\n");

   page_string(ch->desc, Gbuf1, 1);
   return;
}

void shopping_kill(char *arg, P_char ch, P_char keeper, int shop_nr)
{
   char Gbuf1[MAX_STRING_LENGTH];

   switch(shop_index[shop_nr].offense)
      {
      case 0:
         sprintf(Gbuf1, "Don't ever try that again %s!", GET_NAME(ch));
         mobsay(keeper, Gbuf1);
         return;

      case 1:
         sprintf(Gbuf1, "Scram - %s, you midget!", GET_NAME(ch));
         mobsay(keeper, Gbuf1);
         return;

      default:
         return;
      }

   return;
}

/*
allowable buy/sell/list/value cmds

cmd   | arg1   | arg2   | arg3 | arg4
buy   |
buy   | 2
buy   | 2      | mob
buy   | ration
buy   | ration | mob
buy   | 20     | ration
buy   | 20     | ration | mob
buy   | 20     | ration | bag
buy   | 20     | ration | bag  | mob
sell  |
sell  | ration
sell  | ration | mob
list  |
list  | mob
value |
value | ration
value | ration | mob
*/
/* New shop_keeper routine for governing shop interactions. MIAX */
#define MSG_NO_STEAL_HERE "$n is a bloody thief!!"

int shop_keeper(P_char keeper, P_char ch, int cmd, char *arg)
{
   P_char tch = NULL;
   P_obj d_obj = NULL;
   P_obj where_to = NULL;
   char  Gbuf1[MAX_STRING_LENGTH], Gbuf2[MAX_STRING_LENGTH];
   char  arg1[MAX_INPUT_LENGTH] = "", arg2[MAX_STRING_LENGTH] = "";
   char  arg3[MAX_INPUT_LENGTH] = "", arg4[MAX_STRING_LENGTH] = "";
   int i, j, shop_nr, calltype, qty = 1;
   bool arg1_qty = FALSE;
   bool mob_found = FALSE, where_found = FALSE;
   bool arg3_item = FALSE;
#ifdef NEWJUSTICE
   char argm[MAX_STRING_LENGTH], victim_name[MAX_STRING_LENGTH];
#endif

   PARSE_ARG(cmd, calltype, cmd);

   if(calltype == PROC_INITIALIZE)
      return IDX_COMMAND | IDX_PERIODIC | IDX_NPC_DIE;

   for(shop_nr = 0; shop_index[shop_nr].keeper != keeper->nr; shop_nr++)
      ;

   if(shop_index[shop_nr].keeper != keeper->nr)
      dump_core();

   if(calltype == PROC_NPC_DIE)
      {
      if(!shop_index[shop_nr].shop_killable)
         {
         wizlog(51, "Non-attackable shopkeeper, %s, was just killed in [%d].  Find out what happened.", C_NAME(keeper), world[keeper->in_room].number);
         }
      }

   if(calltype == PROC_EVENT)
      {
      if(!AWAKE(keeper) || (keeper->in_room == NOWHERE))
         return TRUE;

      if(!shop_index[shop_nr].shop_killable && !IS_CSET(keeper->only.npc->npcact, ACT_NOKILL))
         SET_CBIT(keeper->only.npc->npcact, ACT_NOKILL);


      if(shop_index[shop_nr].open && !IS_CSET(shop_index[shop_nr].hours, time_info.hour))
         { /* closing time! */
         for(i = 0, j = time_info.hour + 1; i < 24; i++)
            {
            if(j > 23)
               j = 0;

            if(IS_CSET(shop_index[shop_nr].hours, j))
               {
               j++;
               break;
               }
            j++;
            }
         sprintf(Gbuf2, "%d%s", (j % 12) ? (j % 12) : 12,
                 (j == 12) ? " noon" : (j == 0) ? " midnight" : (j > 11) ? "pm" : "am");
         sprintf(Gbuf1, shop_index[shop_nr].SM_shop_close, Gbuf2);
         act(Gbuf1, FALSE, keeper, 0, 0, TO_ROOM);
         shop_index[shop_nr].open = 0;
         }
      else if(!shop_index[shop_nr].open && IS_CSET(shop_index[shop_nr].hours, time_info.hour))
         {
         /* opening time! */
         act(shop_index[shop_nr].SM_shop_open, FALSE, keeper, 0, 0, TO_ROOM);
         shop_index[shop_nr].open = 1;
         }

      return TRUE;
      }

   if(calltype)
      return FALSE;
#ifdef NEWJUSTICE
   if((cmd == CMD_STEAL))
      {
      /* Steal */
      arg = one_argument(arg, argm);
      one_argument(arg, victim_name);
      if(keeper == get_char_room(victim_name, ch->in_room))
         {
         if(CHAR_IN_TOWN(keeper))
            {
            if(IS_SET(hometowns[CHAR_IN_TOWN(keeper) - 1].flags, JUSTICE_EVILHOME) ||
               IS_SET(hometowns[CHAR_IN_TOWN(keeper) - 1].flags, JUSTICE_LEVEL_HARSH))
               {
               justice_send_guards(NOWHERE, ch, MOB_SPEC_KILL, 2);
               crime_add(CHAR_IN_TOWN(keeper), C_NAME(ch), C_NAME(keeper),
                         keeper->in_room, CRIME_ATT_THEFT, time(NULL), J_STATUS_CRIME, 200);
               sprintf(argm, "shout Guards! Kill %s for stealing from my shop!",
                       CAN_SEE(keeper, ch) ? GET_NAME(ch) : "Someone");
               }
            else if(IS_SET(hometowns[CHAR_IN_TOWN(keeper) -1].flags, JUSTICE_GOODHOME))
               {
               justice_send_guards(NOWHERE, ch, MOB_SPEC_ARREST1, 2);
               crime_add(CHAR_IN_TOWN(keeper), C_NAME(ch), C_NAME(keeper),
                         keeper->in_room, CRIME_ATT_THEFT, time(NULL), J_STATUS_CRIME, 200);
               sprintf(argm, "shout Guards! Arrest %s for stealing from my shop!",
                       CAN_SEE(keeper, ch) ? GET_NAME(ch) : "Someone");
               }
            else
               {
               act(MSG_NO_STEAL_HERE, FALSE, ch, 0, keeper, TO_CHAR);
               return TRUE;
               }

            command_interpreter(keeper, argm);
            }
         else
            act(MSG_NO_STEAL_HERE, FALSE, ch, 0, keeper, TO_CHAR);

         return(TRUE);
         }
      }
#endif
#ifdef OLDJUSTICE
   if((cmd == CMD_STEAL) && (shop_index[shop_nr].shop_is_roaming != 1))
      {
      /* Steal */
      act("But how can you steal it when everything is locked behind a display case?",
          FALSE, ch, 0, keeper, TO_CHAR);
      return(TRUE);
      }
#endif
   if(((cmd == CMD_CAST) || (cmd == CMD_RECITE) || (cmd == CMD_USE)) &&
      (!shop_index[shop_nr].magic_allowed &&
       (shop_index[number_of_shops].shop_is_roaming || (keeper->in_room == real_room(GET_HOME(keeper))))))
      {
      /* Cast, recite, use */
      sprintf(Gbuf1, "No magic in here %s!", C_NAME(ch));
      mobsay(keeper, Gbuf1);
      return TRUE;
      }

   if(IS_AGG_CMD(cmd) && !shop_index[shop_nr].shop_killable)
      {
      arg = one_argument(arg, arg1);
      if(*arg1 && (((cmd == CMD_HITALL) && isname("all", arg1)) ||
                   (generic_find(arg1, FIND_CHAR_ROOM, ch, &tch, &d_obj) && (tch == keeper))))
         {
         shopping_kill(arg, ch, keeper, shop_nr);
         return(TRUE);
         }

      return FALSE;
      }

   if(!arg)
      return FALSE;

   if(cmd != CMD_LIST && cmd != CMD_BUY && cmd != CMD_SELL && cmd != CMD_VALUE)
      return FALSE;

   arg = one_argument(arg, arg1);
   if(arg && *arg)
      arg = one_argument(arg, arg2);
   if(arg && *arg)
      arg = one_argument(arg, arg3);
   if(arg && *arg)
      arg = one_argument(arg, arg4);
   //   debuglog(51, DS_AZUTH, "shop_keeper [%s] arg = [%s] arg1 = [%s] arg2 = [%s] arg3 = [%s] arg4 = [%s]", C_NAME(keeper), arg ? arg : "null", arg1, arg2, arg3, arg4);


   if(cmd == CMD_LIST)
      {
      if(!*arg1)
         {
         if(ch->in_room == shop_index[shop_nr].in_room || shop_index[shop_nr].shop_is_roaming)
            {
            // first shopkeep in a room gets raw list, for others you must specify
            shopping_list(arg1, ch, keeper, shop_nr); // arg1 is unused in this
            return TRUE;
            }

         return FALSE; // keeper in odd room, so ignore the cmd
         }
      else
         {
         mob_found = generic_find(arg1, FIND_CHAR_ROOM, ch, &tch, &d_obj);
         if(!mob_found || tch != keeper)
            return FALSE;
         else
            {
            if((ch->in_room == shop_index[shop_nr].in_room) || shop_index[shop_nr].shop_is_roaming)
               {
               // list for a specific shop keeper in the room
               shopping_list(arg1, ch, keeper, shop_nr); // arg1 is unused in this
               return TRUE;
               }

            return FALSE; // keeper in odd room, so ignore the cmd
            }
         }
      }

   if(cmd == CMD_VALUE || cmd == CMD_SELL) // these two virtually identical
      {
      if(!*arg1)
         {
         if(((ch->in_room == shop_index[shop_nr].in_room) || shop_index[shop_nr].shop_is_roaming) && (GET_POS(ch) >= POS_STANDING) && (GET_STAT(ch) > STAT_RESTING))
            {
            if(cmd == CMD_VALUE)
               shopping_value(arg1, ch, keeper, shop_nr); // arg1 will be blank, triggering what to valuate
            else
               shopping_sell(arg1, ch, keeper, shop_nr); // arg1 will be blank, triggering what you selling
            return TRUE;
            }

         return FALSE; // keeper in odd room or the not standing, so ignore the cmd
         }
      else if(*arg2)
         {
         mob_found = generic_find(arg2, FIND_CHAR_ROOM, ch, &tch, &d_obj);
         if(!mob_found || tch != keeper)
            return FALSE;

         if(((ch->in_room == shop_index[shop_nr].in_room) || shop_index[shop_nr].shop_is_roaming) && (GET_POS(ch) >= POS_STANDING) && (GET_STAT(ch) > STAT_RESTING))
            {
            // value/sell for a specific shop keeper in the room
            if(cmd == CMD_VALUE)
               shopping_value(arg1, ch, keeper, shop_nr);
            else
               shopping_sell(arg1, ch, keeper, shop_nr);
            return TRUE;
            }

         return FALSE; // keeper in odd room or the not standing, so ignore the cmd
         }
      else
         {
         if(((ch->in_room == shop_index[shop_nr].in_room) || shop_index[shop_nr].shop_is_roaming) && (GET_POS(ch) >= POS_STANDING) && (GET_STAT(ch) > STAT_RESTING))
            {
            // first shopkeep in a room gets a crack at valuing/buying it, for others you must specify
            if(cmd == CMD_VALUE)
               shopping_value(arg1, ch, keeper, shop_nr);
            else
               shopping_sell(arg1, ch, keeper, shop_nr);
            return TRUE;
            }

         return FALSE; // keeper in odd room or the not standing, so ignore the cmd
         }
      }

   if(cmd == CMD_BUY) // it should be at this point, but check anyways
      {
      qty = 1; // default is buy one
      where_to = NULL; // this represents to char's inventory, it's the default
      arg1_qty = FALSE;
      mob_found = FALSE;
      where_found = FALSE;
      arg3_item = FALSE;

      if(!*arg1)
         {
         if(((ch->in_room == shop_index[shop_nr].in_room) || shop_index[shop_nr].shop_is_roaming) && (GET_POS(ch) >= POS_STANDING) && (GET_STAT(ch) > STAT_RESTING))
            {
            shopping_buy(arg1, ch, keeper, shop_nr, qty, where_to); // arg1 will be blank, triggering what you buying
            return TRUE;
            }

         //         debuglog(51, DS_AZUTH, "return false 4");
         return FALSE; // keeper in odd room or the not standing, so ignore the cmd
         }

      if(*arg4)
         {
         mob_found = generic_find(arg4, FIND_CHAR_ROOM, ch, &tch, &d_obj);
         if(!mob_found || tch != keeper)
            {
            //            debuglog(51, DS_AZUTH, "return false 3");
            return FALSE;
            }

         arg1_qty = TRUE;
         arg3_item = TRUE;
         }

      if(*arg3)
         {
         where_found = generic_find(arg3, FIND_OBJ_INV, ch, &tch, &where_to);
         if(!where_found)
            {
            mob_found = generic_find(arg3, FIND_CHAR_ROOM, ch, &tch, &d_obj);
            if(!mob_found)
               {
               sprintf(Gbuf1, "You don't seem to be able to find '%s'\n", arg3);
               send_to_char(Gbuf1, ch);
               return TRUE;
               }

            if(tch != keeper)
               {
               //               debuglog(51, DS_AZUTH, "return false 2");
               return FALSE;
               }

            arg3_item = FALSE;
            }
         else
            arg3_item = TRUE;

         arg1_qty = TRUE;
         }

      if(*arg2 && !arg1_qty)
         {
         mob_found = generic_find(arg2, FIND_CHAR_ROOM, ch, &tch, &d_obj);
         if(!mob_found)
            arg1_qty = TRUE;
         else
            {
            if(tch != keeper)
               {
               //               debuglog(51, DS_AZUTH, "return false 1");
               return FALSE;
               }
            }
         }

      if(arg1_qty == TRUE)
         {
         if(!is_number(arg1))
            {
            sprintf(Gbuf1, "Sure thing! Just specify how many and we can do business.");
            mobsay(keeper, Gbuf1);
            return TRUE;
            }

         qty = atoi(arg1);
         if(qty < 1)
            {
            sprintf(Gbuf1, "Having trouble with counting are we?");
            mobsay(keeper, Gbuf1);
            return TRUE;
            }

         strcpy(arg1, arg2);
         }

      if(((ch->in_room == shop_index[shop_nr].in_room) || shop_index[shop_nr].shop_is_roaming) && (GET_POS(ch) >= POS_STANDING) && (GET_STAT(ch) > STAT_RESTING))
         {
         //         debuglog(51, DS_AZUTH, "arg1 = [%s] keeper [%s] qty = [%d] where_to %s", arg1, C_NAME(keeper), qty, where_to ? "bag" : "inv");
         shopping_buy(arg1, ch, keeper, shop_nr, qty, where_to);
         return TRUE;
         }
      }

   //   debuglog(51, DS_AZUTH, "Hrmm how'd we get to here?");
   return FALSE;
}

/* Boot routine re-written for new options MIAX */

#define SHOP shop_index[number_of_shops]

/* Original boot_the_shops() function - replaced with boot_the_shops_optimized()
 * This function has been superseded by an optimized version that provides:
 * - Faster keyword lookup using hash tables
 * - Better memory allocation strategy
 * - More efficient string parsing
 * Keeping original code for reference
 */
#if 0
void boot_the_shops(void)
{
   FILE *shop_f;
   byte add_one = TRUE;
   char buf[MAX_STRING_LENGTH + 1], keyw[MAX_STRING_LENGTH], args[MAX_STRING_LENGTH], *t_p;
   int i, this_key, tmp1, tmp2, rval, V_num = -1, flag, shop_ok, prod_save[500];
   int line_count = 0, shop_count = 0;

   /* Starting shop boot process */

   if(!(shop_f = fopen(SHOP_FILE, "r")))
      {
      logit(LOG_STATUS, "No world.shp file, no shops loading.\n");
      /* No shop file found */
      return;
      }

   /* Successfully opened shop file */

   number_of_shops = 0;
   shop_ok = 0;

   for(;;)
      {
      line_count++;
      /* Progress tracking removed for performance */
      
      rval = fget_line(shop_f, buf, MAX_STRING_LENGTH);

      /* Enhanced debugging for lines 6775-6795 and around line 7956 */
      if ((line_count >= 6775 && line_count <= 6795) || (line_count >= 7950 && line_count <= 7960)) {
         logit(LOG_DEBUG, "DEBUG: boot_the_shops() - Line %d: raw buffer='%s' (length=%d)", 
               line_count, buf, (int)strlen(buf));
         fprintf(stderr, "DEBUG: boot_the_shops() - Line %d: raw buffer='%s' (length=%d)\n", 
                 line_count, buf, (int)strlen(buf));
      }

      if(rval == EOF)
         {
         /* Reached EOF */
         /* this will finish off the current shop (if any) and end the routine cleanly */
         strcpy(buf, "SHOP: 999999999");
         }

      if(buf[0] == 0)
         {
         /* Empty line, skipping */
         continue;
         }

      /* parse the 'buf' which should hold a shop files entry in the format <keyword>:[<args>], we use sscanf to
         find the keyword, and strip out the args (as a string) into keyw and args, we then match the keyword
         against the shop_keys[] to be used in the switch.  Various cases will use a second sscanf (on args) to
         provide the correct format for any args.  This allows us to handle any shop keyword in any order. */

      if((t_p = strstr(buf, ":")) == NULL)
         {
         logit(LOG_BOOT, "Bogus string in world.shp: '%s', skipping shop %d", buf, V_num);
         /* Bogus string (no colon found) */
         continue;
         }
      *t_p = '\0';
      t_p++;
      keyw[0] = 0;
      args[0] = 0;
      strcpy(keyw, buf);
      /* Skip any leading spaces after the colon */
      while(isspace(*t_p))
         t_p++;
      strcpy(args, t_p);
      
      /* Enhanced debugging for lines 6775-6795 and around line 7956 - after parsing */
      if ((line_count >= 6775 && line_count <= 6795) || (line_count >= 7950 && line_count <= 7960)) {
         logit(LOG_DEBUG, "DEBUG: boot_the_shops() - Line %d: After parsing: keyw='%s', args='%s'", 
               line_count, keyw, args);
         fprintf(stderr, "DEBUG: boot_the_shops() - Line %d: After parsing: keyw='%s', args='%s'\n", 
                 line_count, keyw, args);
      }
      
      /* Debug log for shops with unusual format */
      if (strncmp(keyw, "SHOP", 4) == 0 && strlen(args) > 0) {
         if (line_count % 100 == 0 || (atoi(args) > 50000 && atoi(args) < 60000)) {
            logit(LOG_DEBUG, "DEBUG: boot_the_shops() - Line %d: keyw='%s', args='%s'", 
                  line_count, keyw, args);
         }
      }

      /* find match for keyw in shop_keys[] */
      for(i = 0, this_key = -1; i < SHOP_KEYWORDS; i++)
         if(!str_cmp(keyw, shop_keys[i].keyword))
            this_key = shop_keys[i].idx;
      
      /* Keyword found */

      if(!shop_ok && (this_key != SHOP_KEY_SHOP))
         {
         /* we are skipping a shop, scanning for a SHOP: # line, so we ignore everything til we find it */
         logit(LOG_BOOT, "boot_the_shops: skipping '%s'", buf);
         continue;
         }

      switch(this_key)
         {
         case -1:
            logit(LOG_BOOT, "Bogus keyword in world.shp: '%s', skipping shop %d", keyw, V_num);
            continue;
            break;

         case SHOP_KEY_MBCASH:
            if(!*args)
               {
               logit(LOG_BOOT, "Shop %5d has malformed MBCASH line", V_num);
               }
            else
               {
               if(SHOP.SM_buyer_broke)
                  {
                  logit(LOG_BOOT, "Shop %5d has multiple MBCASH lines", V_num);
                  free_string(SHOP.SM_buyer_broke);
                  }
               SHOP.SM_buyer_broke = str_dup(args);
               }
            break;

         case SHOP_KEY_MBHAVE:
            if(!*args)
               {
               logit(LOG_BOOT, "Shop %5d has malformed MBHAVE line", V_num);
               }
            else
               {
               if(SHOP.SM_buyer_not_have)
                  {
                  logit(LOG_BOOT, "Shop %5d has multiple MBHAVE lines", V_num);
                  free_string(SHOP.SM_buyer_not_have);
                  }
               SHOP.SM_buyer_not_have = str_dup(args);
               }
            break;

         case SHOP_KEY_MBIGOT:
            if(!*args)
               {
               logit(LOG_BOOT, "Shop %5d has malformed MBIGOT line", V_num);
               }
            else
               {
               if(SHOP.SM_hates)
                  {
                  logit(LOG_BOOT, "Shop %5d has multiple MBIGOT lines", V_num);
                  free_string(SHOP.SM_hates);
                  }
               SHOP.SM_hates = str_dup(args);
               }
            break;

         case SHOP_KEY_MBUY:
            if(!*args)
               {
               logit(LOG_BOOT, "Shop %5d has malformed MBUY line", V_num);
               }
            else
               {
               if(SHOP.SM_buy_from)
                  {
                  logit(LOG_BOOT, "Shop %5d has multiple MBUY lines", V_num);
                  free_string(SHOP.SM_buy_from);
                  }
               SHOP.SM_buy_from = str_dup(args);
               }
            break;

         case SHOP_KEY_MCLOSE:
            if(!*args)
               {
               logit(LOG_BOOT, "Shop %5d has malformed MCLOSE line", V_num);
               }
            else
               {
               if(SHOP.SM_shop_close)
                  {
                  logit(LOG_BOOT, "Shop %5d has multiple MCLOSE lines", V_num);
                  free_string(SHOP.SM_shop_close);
                  }
               SHOP.SM_shop_close = str_dup(args);
               }
            break;

         case SHOP_KEY_MNBUY:
            if(!*args)
               {
               logit(LOG_BOOT, "Shop %5d has malformed MNBUY line", V_num);
               }
            else
               {
               if(SHOP.SM_not_buy)
                  {
                  logit(LOG_BOOT, "Shop %5d has multiple MNBUY lines", V_num);
                  free_string(SHOP.SM_not_buy);
                  }
               SHOP.SM_not_buy = str_dup(args);
               }
            break;

         case SHOP_KEY_MOPEN:
            if(!*args)
               {
               logit(LOG_BOOT, "Shop %5d has malformed MOPEN line", V_num);
               }
            else
               {
               if(SHOP.SM_shop_open)
                  {
                  logit(LOG_BOOT, "Shop %5d has multiple MOPEN lines", V_num);
                  free_string(SHOP.SM_shop_open);
                  }
               SHOP.SM_shop_open = str_dup(args);
               }
            break;

         case SHOP_KEY_MSCASH:
            if(!*args)
               {
               logit(LOG_BOOT, "Shop %5d has malformed MSCASH line", V_num);
               }
            else
               {
               if(SHOP.SM_shop_broke)
                  {
                  logit(LOG_BOOT, "Shop %5d has multiple MSCASH lines", V_num);
                  free_string(SHOP.SM_shop_broke);
                  }
               SHOP.SM_shop_broke = str_dup(args);
               }
            break;

         case SHOP_KEY_MSELL:
            if(!*args)
               {
               logit(LOG_BOOT, "Shop %5d has malformed MSELL line", V_num);
               }
            else
               {
               if(SHOP.SM_sell_to)
                  {
                  logit(LOG_BOOT, "Shop %5d has multiple MSELL lines", V_num);
                  free_string(SHOP.SM_sell_to);
                  }
               SHOP.SM_sell_to = str_dup(args);
               }
            break;

         case SHOP_KEY_MSHAVE:
            if(!*args)
               {
               logit(LOG_BOOT, "Shop %5d has malformed MSHAVE line", V_num);
               }
            else
               {
               if(SHOP.SM_shop_not_have)
                  {
                  logit(LOG_BOOT, "Shop %5d has multiple MSHAVE lines", V_num);
                  free_string(SHOP.SM_shop_not_have);
                  }
               SHOP.SM_shop_not_have = str_dup(args);
               }
            break;

         case SHOP_KEY_BT:

            flag = 0;
            strcat(args, " ");

            t_p = args;
            do
               {
               t_p = strsep(&t_p, " ");
               if(!*t_p)
                  break;
               if(isdigit(*t_p) && ((tmp1 = atoi(t_p)) > -1) && (tmp1 <= LAST_ITEM_TYPE))
                  {
                  flag++;
                  SET_CBIT(SHOP.types, tmp1);
                  }
               else
                  logit(LOG_BOOT, "Shop %5d has invalid BT arg (%s)", V_num, buf);
               t_p += strlen(t_p);
               } while(*t_p);

            break;

         case SHOP_KEY_CASTING:
            if(SHOP.magic_allowed)
               logit(LOG_BOOT, "Shop %5d has multiple CASTING lines.", V_num);
            SHOP.magic_allowed = 1;
            break;

         case SHOP_KEY_KILLABLE:
            if(SHOP.shop_killable)
               logit(LOG_BOOT, "Shop %5d has multiple KILLABLE lines.", V_num);
            SHOP.shop_killable = 1;
            break;

         case SHOP_KEY_ROAMING:
            if(SHOP.shop_is_roaming)
               logit(LOG_BOOT, "Shop %5d has multiple ROAMING lines.", V_num);
            SHOP.shop_is_roaming = 1;
            shop_ok |= 2;
            break;

         case SHOP_KEY_ROOM:
            tmp1 = -1;
            if((sscanf(args, " %d ", &tmp1) != 1) || (tmp1 < 0))
               {
               logit(LOG_BOOT, "Shop %5d has malformed ROOM line.", V_num);
               }
            else
               {
               if((SHOP.in_room = real_room(tmp1)) == NOWHERE)
                  {
                  logit(LOG_BOOT, "Shop %5d has illegal room number (%d) (may just not be loaded)", V_num, tmp1);
                  shop_ok = 0;
                  }
               else
                  shop_ok |= 2;
               }
            break;

         case SHOP_KEY_DEADBEAT:
            tmp1 = -1;
            if((sscanf(args, " %d ", &tmp1) != 1) || (tmp1 < 0))
               {
               logit(LOG_BOOT, "Shop %5d has malformed DEADBEAT line.", V_num);
               }
            else
               {
               SHOP.deadbeat = tmp1;
               }
            break;

         case SHOP_KEY_OFFENSE:
            tmp1 = -1;
            if((sscanf(args, " %d ", &tmp1) != 1) || (tmp1 < 0))
               {
               logit(LOG_BOOT, "Shop %5d has malformed OFFENSE line.", V_num);
               }
            else
               {
               SHOP.offense = tmp1;
               }
            break;

         case SHOP_KEY_PROFIT:
            tmp1 = -1;
            if((sscanf(args, " %d ", &tmp1) != 1))
               {
               logit(LOG_BOOT, "Shop %5d has malformed PROFIT line.", V_num);
               }
            else if((tmp1 < 10) || (tmp1 > 1000))
               {
               logit(LOG_BOOT, "Shop %5d has invalid PROFIT value (%d).", V_num, tmp1);
               }
            else
               {
               SHOP.profit = tmp1;
               }
            break;

         case SHOP_KEY_GREED:
            tmp1 = -1;
            if((sscanf(args, " %d ", &tmp1) != 1))
               {
               logit(LOG_BOOT, "Shop %5d has malformed GRRED line.", V_num);
               }
            else if((tmp1 < 10) || (tmp1 > 1000))
               {
               logit(LOG_BOOT, "Shop %5d has invalid GREED value (%d).", V_num, tmp1);
               }
            else
               {
               SHOP.greed = tmp1;
               }
            break;

         case SHOP_KEY_PO:
            /* we save the real numbers of any objects that are to be produced in a local array.  When shop is done
               we'll CREATE an int array and copy the values into it.  This allows us to have any number without
               huge arrays, and without 1000s of calls to realloc.  JAB */

            if(SHOP.production_count > 499)
               break;

            flag = 0;
            strcat(args, " ");

            t_p = args;
            do
               {
               t_p = strsep(&t_p, " ");
               if(!*t_p)
                  break;
               if(!isdigit(*t_p) || (tmp1 = atoi(t_p)) < 1)
                  {
                  logit(LOG_BOOT, "Shop %5d has invalid PO arg (%d)", V_num, tmp1);
                  }
               else if(SHOP.production_count > 499)
                  {
                  logit(LOG_BOOT, "Shop %5d has silly number of PO values, ignoring further values.", V_num);
                  }
               else
                  {
                  flag++;
                  if((prod_save[SHOP.production_count] = real_object(tmp1)) == NOWHERE)
                     {
                     logit(LOG_BOOT, "Shop %5d has invalid PO arg (%d) (may just not be loaded)", V_num, tmp1);
                     }
                  else
                     SHOP.production_count++;
                  }
               t_p += strlen(t_p);
               } while(*t_p);

            if(!flag)
               logit(LOG_BOOT, "Shop %5d has malformed PO line '%s'", V_num, buf);

            break;

         case SHOP_KEY_SHOP:
            /* several things to keep in mind:
             * don't touch 'args' until all processing of previous shop is complete.
             * to avoid duplication of code, when we hit EOF on world.shp, we come here one final time, but with 'args'
             *       being invalid.
             * JAB */

            switch(shop_ok)
               {
               case 2:
                  logit(LOG_EXIT, "Boggle!  Must be a missing SHOP key at start of world.shp, fix it!");
                  dump_core();
                  break;

               case 1:
                  logit(LOG_BOOT, "Shop %5d is invalid, it must have either a ROAMING: or a ROOM: entry", V_num);
                  add_one = FALSE;
                  break;

               case 0:
                  /* two cases here
                   * 1. this is the first SHOP entry in world.shp
                   * 2. we skipped a mangled shop
                   * only difference in handling, if it's 1. then we call CREATE to start things off, it's it's 2. we
                   *     don't do anything.  The way we tell, is if shop_index is NULL or not.
                   */
                  /* Handle malformed entries like SHOP:50437 with no space */
                  if (strlen(args) == 0) {
                     logit(LOG_BOOT, "SHOP: line has no number, skipping");
                     /* Empty args for SHOP */
                     shop_ok = 0;
                  }
                  else if((sscanf(args, " %d ", &V_num) != 1) || (V_num < 1))
                     {
                     logit(LOG_BOOT, "malformed SHOP: line '%s', skipping", buf);
                     /* Failed to parse shop number */
                     shop_ok = 0;
                     }
                  else
                     {
                     if (V_num > 99999) {
                        logit(LOG_BOOT, "Shop number %d is unreasonably high, skipping", V_num);
                        /* Shop number too high */
                        shop_ok = 0;
                     } else {
                        shop_ok = 1;
                        shop_count++;
                        /* Starting first shop */
                     }
                     }

                  if(shop_ok && !shop_index && (rval != EOF))
                     {  /* first shop */
                     /* CREATE first shop_index */
                     CREATE(shop_index, struct shop_data, 1);
                     add_one = FALSE;
                     /* CREATE completed successfully */
                     }
                  break;

               case 3:
                  /* we have a complete and valid shop, so finish processing and move on */

                  SHOP.keeper = real_mobile(V_num);  /* the final check */

                  if(SHOP.keeper == -1)
                     {
                     /* shop is valid, but the keeper hasn't been loaded (probably his zone isn't in the game), so we
                        treat the shop as invalid and skip it.  */
                     add_one = FALSE;
                     }
                  else
                     {
                     /* first, we set any missing strings to their defaults.
                        The non-string defaults were set at the start */

                     if(!SHOP.SM_shop_open)
                        SHOP.SM_shop_open =
                        str_dup("$n says 'We are open for business, step right up!'");
                     if(!SHOP.SM_shop_close)
                        SHOP.SM_shop_close =
                        str_dup("$n says 'We are closed, we'll reopen at %s.'");
                     if(!SHOP.SM_sell_to)
                        SHOP.SM_sell_to =
                        str_dup("$n says 'Here you go $N, and only %s too!'");
                     if(!SHOP.SM_buy_from)
                        SHOP.SM_buy_from =
                        str_dup("$n says 'Here's your money $N, thanks.'");
                     if(!SHOP.SM_shop_broke)
                        SHOP.SM_shop_broke =
                        str_dup("$n says 'Too rich for my blood $N, maybe you'd like to buy something first?'");
                     if(!SHOP.SM_buyer_broke)
                        SHOP.SM_buyer_broke =
                        str_dup("$n says 'Sorry $N, but you don't have enough money for $p.'");
                     if(!SHOP.SM_shop_not_have)
                        SHOP.SM_shop_not_have =
                        str_dup("$n says 'Sorry $N, but I don't have any $p to sell!'");
                     if(!SHOP.SM_buyer_not_have)
                        SHOP.SM_buyer_not_have =
                        str_dup("$n says 'You'll have to show it to me first, $N!'");
                     if(!SHOP.SM_not_buy)
                        SHOP.SM_not_buy =
                        str_dup("$n says 'I don't handle those kinds of things $N, try somewhere else.'");
                     if(!SHOP.SM_hates)
                        SHOP.SM_hates =
                        str_dup("$n says 'I don't deal with your kind $N!  Begone!'");

                     /* CREATE our production array (assuming there IS any production) */
                     if(SHOP.production_count)
                        {
                        CREATE((SHOP.production), int, (unsigned)(SHOP.production_count));
                        for(i = 0; i < SHOP.production_count; i++)
                           SHOP.production[i] = prod_save[i];
                        }
                     prod_save[0] = -1;
                     add_one = TRUE;  /* RECREATE needed for the next shop */
                     }

                  /* NOW process the new shop number */
                  /* Handle malformed entries like SHOP:50437 with no space */
                  if (strlen(args) == 0) {
                     logit(LOG_BOOT, "SHOP: line has no number, skipping");
                     /* Empty args for SHOP */
                     shop_ok = 0;
                  }
                  else if((sscanf(args, " %d ", &V_num) != 1) || (V_num < 1))
                     {
                     logit(LOG_BOOT, "malformed SHOP: line '%s', skipping", buf);
                     /* Failed to parse shop number */
                     shop_ok = 0;
                     }
                  else
                     {
                     if (V_num > 99999) {
                        logit(LOG_BOOT, "Shop number %d is unreasonably high, skipping", V_num);
                        /* Shop number too high */
                        shop_ok = 0;
                     } else {
                        shop_ok = 1;
                        shop_count++;
                        /* Starting subsequent shop */
                     }
                     }

                  break;

               default:
                  logit(LOG_BOOT, "Impossible shop_ok value (%d) in boot_the_shops", shop_ok);
                  dump_core();
               }

            if(!add_one)
               {  /* shop skipped for some reason */

               /* clean up from previous aborted shop */

               if(SHOP.SM_shop_open)
                  free_string(SHOP.SM_shop_open);
               if(SHOP.SM_shop_close)
                  free_string(SHOP.SM_shop_close);
               if(SHOP.SM_sell_to)
                  free_string(SHOP.SM_sell_to);
               if(SHOP.SM_buy_from)
                  free_string(SHOP.SM_buy_from);
               if(SHOP.SM_shop_broke)
                  free_string(SHOP.SM_shop_broke);
               if(SHOP.SM_buyer_broke)
                  free_string(SHOP.SM_buyer_broke);
               if(SHOP.SM_shop_not_have)
                  free_string(SHOP.SM_shop_not_have);
               if(SHOP.SM_buyer_not_have)
                  free_string(SHOP.SM_buyer_not_have);
               if(SHOP.SM_not_buy)
                  free_string(SHOP.SM_not_buy);
               if(SHOP.SM_hates)
                  free_string(SHOP.SM_hates);

               }
            else if(rval != EOF)
               {
               number_of_shops++;
               /* RECREATE shop_index */
               
               /* Defensive check before RECREATE */
               if (number_of_shops > 10000) {
                  logit(LOG_BOOT, "ERROR: Too many shops (%d), aborting shop load", number_of_shops);
                  fprintf(stderr, "ERROR: Too many shops (%d), aborting shop load\n", number_of_shops);
                  fclose(shop_f);
                  return;
               }
               
               /* Add debugging for memory state before RECREATE */
               if ((line_count >= 6775 && line_count <= 6795) || (line_count >= 7950 && line_count <= 7960)) {
                  logit(LOG_DEBUG, "DEBUG: boot_the_shops() - Before RECREATE: shop_index=%p, size=%d", 
                        shop_index, number_of_shops + 1);
                  fprintf(stderr, "DEBUG: boot_the_shops() - Before RECREATE: shop_index=%p, size=%d\n", 
                          shop_index, number_of_shops + 1);
               }
               
               RECREATE(shop_index, struct shop_data, (number_of_shops + 1));
               add_one = FALSE;
               
               /* Add debugging for memory state after RECREATE */
               if ((line_count >= 6775 && line_count <= 6795) || (line_count >= 7950 && line_count <= 7960)) {
                  logit(LOG_DEBUG, "DEBUG: boot_the_shops() - After RECREATE: shop_index=%p", shop_index);
                  fprintf(stderr, "DEBUG: boot_the_shops() - After RECREATE: shop_index=%p\n", shop_index);
               }
               
               /* RECREATE completed successfully */
               }

            if(rval == EOF)
               {
               /* Reached end of processing, cleaning up */
               if((SHOP.keeper == -1) && !number_of_shops)
                  logit(LOG_BOOT, "world.shp reading complete, no shops.");
               else
                  logit(LOG_BOOT, "world.shp reading complete, %d shops.", number_of_shops + 1);
               fclose(shop_f);
               /* Function completed successfully */
               return;
               }

            /* Enhanced debugging for bzero operation */
            if ((line_count >= 6775 && line_count <= 6795) || (line_count >= 7950 && line_count <= 7960)) {
               logit(LOG_DEBUG, "DEBUG: boot_the_shops() - Line %d: About to bzero shop_index[%d] at address %p", 
                     line_count, number_of_shops, &(SHOP));
               fprintf(stderr, "DEBUG: boot_the_shops() - Line %d: About to bzero shop_index[%d] at address %p\n", 
                       line_count, number_of_shops, &(SHOP));
            }
            
            /* Calling bzero on shop_index */
            
            /* Defensive check */
            if (!shop_index) {
               logit(LOG_BOOT, "ERROR: shop_index is NULL before bzero!");
               fprintf(stderr, "ERROR: shop_index is NULL before bzero!\n");
               return;
            }
            
            bzero(&(SHOP), sizeof(struct shop_data));
            /* bzero completed successfully */
            
            if ((line_count >= 6775 && line_count <= 6795) || (line_count >= 7950 && line_count <= 7960)) {
               logit(LOG_DEBUG, "DEBUG: boot_the_shops() - Line %d: bzero completed for shop_index[%d]", 
                     line_count, number_of_shops);
               fprintf(stderr, "DEBUG: boot_the_shops() - Line %d: bzero completed for shop_index[%d]\n", 
                       line_count, number_of_shops);
            }

            /* init to the defaults (except the strings), that way we don't have to worry about setting them in each
               section below.  At this point, shop_index[number_of_shops] has been zeroed out (by bzero), so we only
               have to set the non-zero default values.  JAB */

            SHOP.keeper = -1;
            SHOP.in_room = -1;
            SHOP.greed = 100;
            SHOP.profit = 100;
            SET_CBIT(SHOP.hates, BIGOT_NPC);
            SHOP.open = 1;

            break;

         case SHOP_KEY_HOURS:

            flag = 0;
            t_p = args;
            while(1)
               {
               tmp1 = tmp2 = -1;
               if(sscanf(t_p, " %d-%d ", &tmp1, &tmp2) != 2)
                  {
                  tmp1 = tmp2 = -1;
                  if(sscanf(t_p, " %d,%d ", &tmp1, &tmp2) != 2)
                     {
                     tmp1 = tmp2 = -1;
                     keyw[0] = 0;
                     if((sscanf(t_p, " %[OCoc] ", keyw) != 1) || (strlen(keyw) != 24))
                        {
                        if(!flag)
                           logit(LOG_BOOT, "Shop %5d has malformed HOURS line '%s'.", V_num, buf);
                        break;
                        }
                     else
                        {
                        /* format of OCOCOCOC... */
                        for(i = 0; i < 24; i++)
                           if((keyw[i] == 'C') || (keyw[i] == 'c'))
                              SET_CBIT((SHOP.hours), i);
                           else
                              REMOVE_CBIT((SHOP.hours), i);

                        break;
                        }
                     }
                  }

               if((tmp1 > tmp2) || (tmp1 < 0))
                  {
                  if(!flag)
                     logit(LOG_BOOT, "Shop %5d has malformed HOURS line '%s'.", V_num, buf);
                  break;
                  }
               if(tmp2 > 23)
                  tmp2 = 23;

               for(i = tmp1; i <= tmp2; i++)
                  SET_CBIT((SHOP.hours), i);

               flag = 1;
               t_p += 4 + (tmp1 > 9) + (tmp2 > 9);
               if((t_p - args) >= strlen(args))
                  break;
               }

            break;

         case SHOP_KEY_CHEATS:
         case SHOP_KEY_HATES:
            /* these are handled identically, just to different variables, so we handle them together until we have
             * to assign values (or report problems). */

            t_p = args;
            while(*t_p)
               {
               tmp2 = 0;  /* Reset match flag for each token */
               while(isspace(*t_p) && (*t_p != '\0'))
                  t_p++;

               for(i = 0; shop_bigot_table[i].race != -1; i++)
                  {
                  tmp1 = strlen(shop_bigot_table[i].code);
                  if(!strn_cmp(t_p, shop_bigot_table[i].code, (unsigned)tmp1))
                     if((*(t_p + tmp1) == ' ') || (*(t_p + tmp1) == '\0'))
                        {
                        /* we have a match */
                        tmp2 = 1;
                        t_p += tmp1;
                        if(this_key == SHOP_KEY_HATES)
                           SET_CBIT((SHOP.hates), ((int)(shop_bigot_table[i].race)));
                        else
                           SET_CBIT((SHOP.cheats), ((int)(shop_bigot_table[i].race)));
                        }
                  }
               if(!tmp2)
                  {
                  logit(LOG_BOOT, "Shop %5d has unknown flag in %s line. '%s'",
                        V_num, (this_key == SHOP_KEY_HATES) ? "HATES" : "CHEATS", buf);
                  while(!isspace(*t_p) && (*t_p != '\0'))
                     t_p++;
                  }
               }

            break;

         default:
            logit(LOG_EXIT, "Unknown Keyword in world.shp: '%s', BOOM!", keyw);
            dump_core();
         }
      }

   logit(LOG_EXIT, "Mangled world.shp file, probably incomplete and/or incorrect loading.");
   fclose(shop_f);
   dump_core();
}
#endif /* 0 - end of original boot_the_shops() */
#undef SHOP

void boot_the_shops_optimized(void)
{
   FILE *shop_f;
   char buf[MAX_STRING_LENGTH + 1], keyw[64], *args, *t_p;
   int i, this_key, tmp1, tmp2, rval, V_num = -1, flag, shop_ok;
   int prod_save[500];
   int shop_capacity = 1000;  /* Initial capacity estimate */
   int shop_count = 0;
   int add_one = TRUE;
   struct shop_data current_shop;
   
   /* Hash table for keyword lookup - simple implementation */
   int keyword_hash[256];  /* Indexed by first char */
   
   /* Initialize hash table */
   for(i = 0; i < 256; i++)
      keyword_hash[i] = -1;
   
   /* Build hash table for first character lookup */
   for(i = 0; i < SHOP_KEYWORDS; i++)
      {
      unsigned char first_char = (unsigned char)shop_keys[i].keyword[0];
      if(keyword_hash[first_char] == -1)
         keyword_hash[first_char] = i;
      }

   if(!(shop_f = fopen(SHOP_FILE, "r")))
      {
      logit(LOG_STATUS, "No world.shp file, no shops loading.\n");
      return;
      }

   /* Initialize shop_index as NULL first */
   shop_index = NULL;
   number_of_shops = 0;
   shop_ok = 0;
   
   /* Initialize current shop with defaults */
   bzero(&current_shop, sizeof(struct shop_data));
   current_shop.keeper = -1;
   current_shop.in_room = -1;
   current_shop.greed = 100;
   current_shop.profit = 100;
   SET_CBIT(current_shop.hates, BIGOT_NPC);
   current_shop.open = 1;
   current_shop.production_count = 0;

   for(;;)
      {
      rval = fget_line(shop_f, buf, MAX_STRING_LENGTH);
      
      if(rval == EOF)
         {
         /* Simulate final SHOP entry to process last shop */
         strcpy(buf, "SHOP: 999999999");
         }
         
      if(buf[0] == 0)
         continue;

      /* Fast keyword parsing */
      t_p = strchr(buf, ':');
      if(!t_p)
         {
         logit(LOG_BOOT, "Bogus string in world.shp: '%s', skipping shop %d", buf, V_num);
         continue;
         }
      
      /* Extract keyword and args efficiently */
      *t_p = '\0';
      strcpy(keyw, buf);
      args = t_p + 1;
      while(*args && isspace(*args))
         args++;

      /* Fast keyword lookup using hash */
      this_key = -1;
      if(keyw[0])
         {
         int start_idx = keyword_hash[(unsigned char)keyw[0]];
         if(start_idx != -1)
            {
            for(i = start_idx; i < SHOP_KEYWORDS && shop_keys[i].keyword[0] == keyw[0]; i++)
               {
               if(!strcmp(keyw, shop_keys[i].keyword))
                  {
                  this_key = shop_keys[i].idx;
                  break;
                  }
               }
            }
         }

      if(!shop_ok && (this_key != SHOP_KEY_SHOP))
         continue;

      switch(this_key)
         {
         case -1:
            logit(LOG_BOOT, "Bogus keyword in world.shp: '%s', skipping shop %d", keyw, V_num);
            continue;

         case SHOP_KEY_MBCASH:
            if(!*args)
               {
               logit(LOG_BOOT, "Shop %5d has malformed MBCASH line", V_num);
               }
            else
               {
               if(current_shop.SM_buyer_broke)
                  {
                  logit(LOG_BOOT, "Shop %5d has multiple MBCASH lines", V_num);
                  free_string(current_shop.SM_buyer_broke);
                  }
               current_shop.SM_buyer_broke = str_dup(args);
               }
            break;

         case SHOP_KEY_MBHAVE:
            if(!*args)
               {
               logit(LOG_BOOT, "Shop %5d has malformed MBHAVE line", V_num);
               }
            else
               {
               if(current_shop.SM_buyer_not_have)
                  {
                  logit(LOG_BOOT, "Shop %5d has multiple MBHAVE lines", V_num);
                  free_string(current_shop.SM_buyer_not_have);
                  }
               current_shop.SM_buyer_not_have = str_dup(args);
               }
            break;

         case SHOP_KEY_MBIGOT:
            if(!*args)
               {
               logit(LOG_BOOT, "Shop %5d has malformed MBIGOT line", V_num);
               }
            else
               {
               if(current_shop.SM_hates)
                  {
                  logit(LOG_BOOT, "Shop %5d has multiple MBIGOT lines", V_num);
                  free_string(current_shop.SM_hates);
                  }
               current_shop.SM_hates = str_dup(args);
               }
            break;

         case SHOP_KEY_MBUY:
            if(!*args)
               {
               logit(LOG_BOOT, "Shop %5d has malformed MBUY line", V_num);
               }
            else
               {
               if(current_shop.SM_buy_from)
                  {
                  logit(LOG_BOOT, "Shop %5d has multiple MBUY lines", V_num);
                  free_string(current_shop.SM_buy_from);
                  }
               current_shop.SM_buy_from = str_dup(args);
               }
            break;

         case SHOP_KEY_MCLOSE:
            if(!*args)
               {
               logit(LOG_BOOT, "Shop %5d has malformed MCLOSE line", V_num);
               }
            else
               {
               if(current_shop.SM_shop_close)
                  {
                  logit(LOG_BOOT, "Shop %5d has multiple MCLOSE lines", V_num);
                  free_string(current_shop.SM_shop_close);
                  }
               current_shop.SM_shop_close = str_dup(args);
               }
            break;

         case SHOP_KEY_MNBUY:
            if(!*args)
               {
               logit(LOG_BOOT, "Shop %5d has malformed MNBUY line", V_num);
               }
            else
               {
               if(current_shop.SM_not_buy)
                  {
                  logit(LOG_BOOT, "Shop %5d has multiple MNBUY lines", V_num);
                  free_string(current_shop.SM_not_buy);
                  }
               current_shop.SM_not_buy = str_dup(args);
               }
            break;

         case SHOP_KEY_MOPEN:
            if(!*args)
               {
               logit(LOG_BOOT, "Shop %5d has malformed MOPEN line", V_num);
               }
            else
               {
               if(current_shop.SM_shop_open)
                  {
                  logit(LOG_BOOT, "Shop %5d has multiple MOPEN lines", V_num);
                  free_string(current_shop.SM_shop_open);
                  }
               current_shop.SM_shop_open = str_dup(args);
               }
            break;

         case SHOP_KEY_MSCASH:
            if(!*args)
               {
               logit(LOG_BOOT, "Shop %5d has malformed MSCASH line", V_num);
               }
            else
               {
               if(current_shop.SM_shop_broke)
                  {
                  logit(LOG_BOOT, "Shop %5d has multiple MSCASH lines", V_num);
                  free_string(current_shop.SM_shop_broke);
                  }
               current_shop.SM_shop_broke = str_dup(args);
               }
            break;

         case SHOP_KEY_MSELL:
            if(!*args)
               {
               logit(LOG_BOOT, "Shop %5d has malformed MSELL line", V_num);
               }
            else
               {
               if(current_shop.SM_sell_to)
                  {
                  logit(LOG_BOOT, "Shop %5d has multiple MSELL lines", V_num);
                  free_string(current_shop.SM_sell_to);
                  }
               current_shop.SM_sell_to = str_dup(args);
               }
            break;

         case SHOP_KEY_MSHAVE:
            if(!*args)
               {
               logit(LOG_BOOT, "Shop %5d has malformed MSHAVE line", V_num);
               }
            else
               {
               if(current_shop.SM_shop_not_have)
                  {
                  logit(LOG_BOOT, "Shop %5d has multiple MSHAVE lines", V_num);
                  free_string(current_shop.SM_shop_not_have);
                  }
               current_shop.SM_shop_not_have = str_dup(args);
               }
            break;

         case SHOP_KEY_BT:
            flag = 0;
            strcat(args, " ");
            
            t_p = args;
            do
               {
               t_p = strsep(&t_p, " ");
               if(!*t_p)
                  break;
               if(isdigit(*t_p) && ((tmp1 = atoi(t_p)) > -1) && (tmp1 <= LAST_ITEM_TYPE))
                  {
                  flag++;
                  SET_CBIT(current_shop.types, tmp1);
                  }
               else
                  logit(LOG_BOOT, "Shop %5d has invalid BT arg (%s)", V_num, buf);
               t_p += strlen(t_p);
               } while(*t_p);
            break;

         case SHOP_KEY_CASTING:
            current_shop.magic_allowed = 1;
            break;

         case SHOP_KEY_KILLABLE:
            current_shop.shop_killable = 1;
            break;

         case SHOP_KEY_ROAMING:
            current_shop.shop_is_roaming = 1;
            shop_ok |= 2;
            break;

         case SHOP_KEY_ROOM:
            tmp1 = -1;
            if((sscanf(args, " %d ", &tmp1) != 1) || (tmp1 < 0))
               {
               logit(LOG_BOOT, "Shop %5d has malformed ROOM line.", V_num);
               }
            else
               {
               if((current_shop.in_room = real_room(tmp1)) == NOWHERE)
                  {
                  logit(LOG_BOOT, "Shop %5d has illegal room number (%d) (may just not be loaded)", V_num, tmp1);
                  shop_ok = 0;
                  }
               else
                  shop_ok |= 2;
               }
            break;

         case SHOP_KEY_DEADBEAT:
            if(sscanf(args, " %d ", &tmp1) == 1 && tmp1 >= 0)
               current_shop.deadbeat = tmp1;
            break;

         case SHOP_KEY_OFFENSE:
            if(sscanf(args, " %d ", &tmp1) == 1 && tmp1 >= 0)
               current_shop.offense = tmp1;
            break;

         case SHOP_KEY_PROFIT:
            if(sscanf(args, " %d ", &tmp1) == 1 && tmp1 >= 10 && tmp1 <= 1000)
               current_shop.profit = tmp1;
            break;

         case SHOP_KEY_GREED:
            tmp1 = -1;
            if((sscanf(args, " %d ", &tmp1) != 1))
               {
               logit(LOG_BOOT, "Shop %5d has malformed GREED line.", V_num);
               }
            else if((tmp1 < 10) || (tmp1 > 1000))
               {
               logit(LOG_BOOT, "Shop %5d has invalid GREED value (%d).", V_num, tmp1);
               }
            else
               {
               current_shop.greed = tmp1;
               }
            break;

         case SHOP_KEY_PO:
            if(current_shop.production_count > 499)
               break;
               
            flag = 0;
            strcat(args, " ");
            
            t_p = args;
            do
               {
               t_p = strsep(&t_p, " ");
               if(!*t_p)
                  break;
               if(!isdigit(*t_p) || (tmp1 = atoi(t_p)) < 1)
                  {
                  logit(LOG_BOOT, "Shop %5d has invalid PO arg (%d)", V_num, tmp1);
                  }
               else if(current_shop.production_count > 499)
                  {
                  logit(LOG_BOOT, "Shop %5d has silly number of PO values, ignoring further values.", V_num);
                  }
               else
                  {
                  flag++;
                  if((prod_save[current_shop.production_count] = real_object(tmp1)) == NOWHERE)
                     {
                     logit(LOG_BOOT, "Shop %5d has invalid PO arg (%d) (may just not be loaded)", V_num, tmp1);
                     }
                  else
                     current_shop.production_count++;
                  }
               t_p += strlen(t_p);
               } while(*t_p);
               
            if(!flag)
               logit(LOG_BOOT, "Shop %5d has malformed PO line '%s'", V_num, buf);
            break;

         case SHOP_KEY_SHOP:
            switch(shop_ok)
               {
               case 2:
                  logit(LOG_EXIT, "Boggle!  Must be a missing SHOP key at start of world.shp, fix it!");
                  dump_core();
                  break;

               case 1:
                  logit(LOG_BOOT, "Shop %5d is invalid, it must have either a ROAMING: or a ROOM: entry", V_num);
                  add_one = FALSE;
                  break;

               case 0:
                  /* First shop or skipped shop */
                  if(strlen(args) == 0)
                     {
                     logit(LOG_BOOT, "SHOP: line has no number, skipping");
                     shop_ok = 0;
                     }
                  else if((sscanf(args, " %d ", &V_num) != 1) || (V_num < 1))
                     {
                     logit(LOG_BOOT, "malformed SHOP: line '%s', skipping", buf);
                     shop_ok = 0;
                     }
                  else
                     {
                     if(V_num > 99999)
                        {
                        logit(LOG_BOOT, "Shop number %d is unreasonably high, skipping", V_num);
                        shop_ok = 0;
                        }
                     else
                        {
                        shop_ok = 1;
                        shop_count++;
                        }
                     }
                  
                  if(shop_ok && !shop_index && (rval != EOF))
                     {
                     /* First shop */
                     CREATE(shop_index, struct shop_data, shop_capacity);
                     add_one = FALSE;
                     }
                  break;

               case 3:
                  /* Save previous shop if valid */
                  current_shop.keeper = real_mobile(V_num);
                  
                  if(current_shop.keeper == -1)
                     {
                     /* Invalid keeper, skip shop */
                     add_one = FALSE;
                     }
                  else
                     {
                     /* Set default strings if missing */
                     if(!current_shop.SM_shop_open)
                        current_shop.SM_shop_open = str_dup("$n says 'We are open for business, step right up!'");
                     if(!current_shop.SM_shop_close)
                        current_shop.SM_shop_close = str_dup("$n says 'We are closed, we'll reopen at %s.'");
                     if(!current_shop.SM_sell_to)
                        current_shop.SM_sell_to = str_dup("$n says 'Here you go $N, and only %s too!'");
                     if(!current_shop.SM_buy_from)
                        current_shop.SM_buy_from = str_dup("$n says 'Here's your money $N, thanks.'");
                     if(!current_shop.SM_shop_broke)
                        current_shop.SM_shop_broke = str_dup("$n says 'Too rich for my blood $N, maybe you'd like to buy something first?'");
                     if(!current_shop.SM_buyer_broke)
                        current_shop.SM_buyer_broke = str_dup("$n says 'Sorry $N, but you don't have enough money for $p.'");
                     if(!current_shop.SM_shop_not_have)
                        current_shop.SM_shop_not_have = str_dup("$n says 'Sorry $N, but I don't have any $p to sell!'");
                     if(!current_shop.SM_buyer_not_have)
                        current_shop.SM_buyer_not_have = str_dup("$n says 'You'll have to show it to me first, $N!'");
                     if(!current_shop.SM_not_buy)
                        current_shop.SM_not_buy = str_dup("$n says 'I don't handle those kinds of things $N, try somewhere else.'");
                     if(!current_shop.SM_hates)
                        current_shop.SM_hates = str_dup("$n says 'I don't deal with your kind $N!  Begone!'");

                     /* Handle production array */
                     if(current_shop.production_count > 0)
                        {
                        CREATE(current_shop.production, int, current_shop.production_count);
                        memcpy(current_shop.production, prod_save, current_shop.production_count * sizeof(int));
                        }

                     /* Expand shop_index if needed */
                     if(number_of_shops >= shop_capacity)
                        {
                        shop_capacity *= 2;
                        RECREATE(shop_index, struct shop_data, shop_capacity);
                        }

                     /* Copy shop to array */
                     shop_index[number_of_shops] = current_shop;
                     number_of_shops++;
                     add_one = TRUE;
                     }

                  /* NOW process new shop number */
                  if(strlen(args) == 0)
                     {
                     logit(LOG_BOOT, "SHOP: line has no number, skipping");
                     shop_ok = 0;
                     }
                  else if((sscanf(args, " %d ", &V_num) != 1) || (V_num < 1))
                     {
                     logit(LOG_BOOT, "malformed SHOP: line '%s', skipping", buf);
                     shop_ok = 0;
                     }
                  else
                     {
                     if(V_num > 99999)
                        {
                        logit(LOG_BOOT, "Shop number %d is unreasonably high, skipping", V_num);
                        shop_ok = 0;
                        }
                     else
                        {
                        shop_ok = 1;
                        shop_count++;
                        }
                     }
                  break;

               default:
                  logit(LOG_BOOT, "Impossible shop_ok value (%d) in boot_the_shops", shop_ok);
                  dump_core();
               }

            if(!add_one)
               {
               /* Clean up failed shop */
               if(current_shop.SM_shop_open)
                  free_string(current_shop.SM_shop_open);
               if(current_shop.SM_shop_close)
                  free_string(current_shop.SM_shop_close);
               if(current_shop.SM_sell_to)
                  free_string(current_shop.SM_sell_to);
               if(current_shop.SM_buy_from)
                  free_string(current_shop.SM_buy_from);
               if(current_shop.SM_shop_broke)
                  free_string(current_shop.SM_shop_broke);
               if(current_shop.SM_buyer_broke)
                  free_string(current_shop.SM_buyer_broke);
               if(current_shop.SM_shop_not_have)
                  free_string(current_shop.SM_shop_not_have);
               if(current_shop.SM_buyer_not_have)
                  free_string(current_shop.SM_buyer_not_have);
               if(current_shop.SM_not_buy)
                  free_string(current_shop.SM_not_buy);
               if(current_shop.SM_hates)
                  free_string(current_shop.SM_hates);
               }
            else if(rval != EOF)
               {
               /* Continue normally - no need to expand, we pre-allocated */
               add_one = FALSE;
               }

            if(rval == EOF)
               {
               /* End of file processing */
               if((current_shop.keeper == -1) && !number_of_shops)
                  logit(LOG_BOOT, "world.shp reading complete, no shops.");
               else
                  logit(LOG_BOOT, "world.shp reading complete, %d shops.", number_of_shops);
               fclose(shop_f);
               
               /* Trim shop_index to actual size */
               if(number_of_shops > 0 && number_of_shops < shop_capacity)
                  RECREATE(shop_index, struct shop_data, number_of_shops);
               
               return;
               }

            /* Reset for new shop */
            bzero(&current_shop, sizeof(struct shop_data));
            current_shop.keeper = -1;
            current_shop.in_room = -1;
            current_shop.greed = 100;
            current_shop.profit = 100;
            SET_CBIT(current_shop.hates, BIGOT_NPC);
            current_shop.open = 1;
            current_shop.production_count = 0;
            break;

         case SHOP_KEY_HOURS:
            t_p = args;
            if(strchr(t_p, 'O') || strchr(t_p, 'C'))
               {
               /* OCOC format */
               for(i = 0; i < 24 && t_p[i]; i++)
                  {
                  if(t_p[i] == 'C' || t_p[i] == 'c')
                     SET_CBIT(current_shop.hours, i);
                  else
                     REMOVE_CBIT(current_shop.hours, i);
                  }
               }
            else
               {
               /* Range format */
               while(sscanf(t_p, " %d-%d ", &tmp1, &tmp2) == 2 || 
                     sscanf(t_p, " %d,%d ", &tmp1, &tmp2) == 2)
                  {
                  if(tmp1 >= 0 && tmp1 <= tmp2 && tmp2 <= 23)
                     {
                     for(i = tmp1; i <= tmp2; i++)
                        SET_CBIT(current_shop.hours, i);
                     }
                  t_p = strchr(t_p, ' ');
                  if(!t_p) break;
                  t_p++;
                  }
               }
            break;

         case SHOP_KEY_CHEATS:
         case SHOP_KEY_HATES:
            t_p = args;
            while(*t_p)
               {
               while(isspace(*t_p)) t_p++;
               if(!*t_p) break;

               for(i = 0; shop_bigot_table[i].race != -1; i++)
                  {
                  tmp1 = strlen(shop_bigot_table[i].code);
                  if(!strn_cmp(t_p, shop_bigot_table[i].code, (unsigned)tmp1) &&
                     (t_p[tmp1] == ' ' || t_p[tmp1] == '\0'))
                     {
                     if(this_key == SHOP_KEY_HATES)
                        SET_CBIT(current_shop.hates, (int)shop_bigot_table[i].race);
                     else
                        SET_CBIT(current_shop.cheats, (int)shop_bigot_table[i].race);
                     t_p += tmp1;
                     break;
                     }
                  }
               
               if(shop_bigot_table[i].race == -1)
                  {
                  logit(LOG_BOOT, "Shop %5d has unknown flag in %s line. '%s'",
                        V_num, (this_key == SHOP_KEY_HATES) ? "HATES" : "CHEATS", buf);
                  /* Skip unknown token */
                  while(*t_p && !isspace(*t_p)) t_p++;
                  }
               }
            break;

         default:
            logit(LOG_EXIT, "Unknown Keyword in world.shp: '%s', BOOM!", keyw);
            dump_core();
         }
      }

   /* Should never reach here - file processing handled in SHOP_KEY_SHOP case */
   logit(LOG_EXIT, "Mangled world.shp file, probably incomplete and/or incorrect loading.");
   fclose(shop_f);
   dump_core();
}

void assign_the_shopkeepers(void)
{
   int temp1;

   /* Starting assignment for shops */

   for(temp1 = 0; temp1 < number_of_shops; temp1++)
      {
      /* Assigning shop */
      if(shop_index[temp1].keeper < 0)
         {
         /* WARNING: Shop has invalid keeper index */
         continue;
         }
      AddProcMob(mob_index[shop_index[temp1].keeper].virtual, shop_keeper, "shopkeep");
      }
   
   /* Completed assignment */
}

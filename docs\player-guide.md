# Player Guide

This guide covers core gameplay and command usage for Outcast MUD. It reflects current systems and common commands implemented by the codebase.

## Table of Contents

1. [Getting Started](#getting-started)
2. [Basic Commands](#basic-commands)
3. [Character Creation](#character-creation)
4. [Movement and Navigation](#movement-and-navigation)
5. [Communication](#communication)
6. [Combat Commands](#combat-commands)
7. [Magic and Skills](#magic-and-skills)
8. [Equipment and Inventory](#equipment-and-inventory)
9. [Character Development](#character-development)
10. [Advanced Systems](#advanced-systems)
11. [Tips](#tips)
12. [Abbreviations](#abbreviations)
13. [Help](#help)

## Getting Started

### Connect

- Use a MUD client (recommended) or telnet.
- Obtain host and port from your server admin/site. Examples use port 4000.
- At login: enter your character name; new players follow creation prompts; returning players enter passwords.

### First Steps

- Read `help newbie`.
- Explore the starting city (Waterdeep in most distributions).
- Visit equipment shops and your class trainer.
- Begin with easy fights in newbie areas.

## Basic Commands

```
look / l                # Examine surroundings
look <object>           # Examine specific item or character
inventory / i           # View carried items
equipment / eq          # View worn equipment
who                     # Show online players
score                   # Character summary
save                    # Save your character
quit                    # Leave the game (must be safe)
commands                # List available commands
help [topic]            # Help index or specific topic
```

## Character Creation

### Races (examples)

- Human: balanced
- Elf: +DEX, -CON, infravision
- Dwarf: +CON, -CHA, infravision
- Halfling: +DEX, -STR, good saves
- Half-Elf: mixed traits, infravision

### Classes (examples)

- Warrior: melee specialist
- Cleric: divine magic and healing
- Thief: stealth and precision
- Mage: arcane caster

### Stats

- STR, INT, WIS, DEX, CON, CHA. Align priorities with class needs.

### Hometowns

- Waterdeep recommended for new players; other cities may have restrictions.

## Movement and Navigation

```
n/s/e/w, u/d            # Move
ne/nw/se/sw             # Diagonals (if supported)
exits                   # Show available exits
```

Notes:
- Some distributions expose a map command; if unavailable, use notes and room descriptions.

Advanced movement (if applicable):
```
enter <portal>
leave
mount <creature> / dismount
fly / land
swim
```

Aids:
```
scan [direction]
where
track <target>          # Ranger/Thief skill dependent
```

## Communication

Channels and messaging:
```
say <message>           # Room chat
'<message>              # Shortcut for say
tell <player> <message> # Private message
reply <message>         # Reply to last tell
gossip <message>        # Global chat (availability/config dependent)
shout <message>         # Zone/global depending on config
```

Socials:
```
emote <action>
bow, wave, smile, nod, ...
```

Groups:
```
group                   # View group info
follow <leader>
group <name>            # Leader adds member
ungroup <member>
gtell <message>
```

## Combat Commands

Basics:
```
kill <target>           # Start combat
k <target>              # Shortcut
hit <target>            # Alias for kill
flee                    # Attempt escape
wimpy <hp>              # Auto-flee threshold
```

Skills (availability depends on class/level):
```
bash, kick              # Warrior basics
backstab <target>       # Thief
circle                  # Thief specialization
rescue <player>         # Warrior
disarm, trip
```

Settings:
```
config +autoassist
config +autoloot
brief
compact
```

After combat:
```
get all corpse
get coins corpse
sac corpse
```

## Magic and Skills

Casting:
```
cast '<spell>' <target>
c '<spell>' <target>     # Shortcut
# Examples:
cast 'magic missile' goblin
c 'cure light' self
c 'armor'
```

Spell/skill management:
```
spells                  # Known spells
memorize <spell>        # Mages (spell slots/memorization system)
mem <spell>             # Shortcut
forget <spell>
meditate                # Faster memorization (if enabled)
pray                    # Cleric recovery (if enabled)

skills                  # Known skills
practice                # Show sessions
practice <skill>        # Train at trainer
```

Magical items:
```
recite <scroll> <target>
quaff <potion>
use <wand> <target>
brew / scribe           # If skills enabled
```

## Equipment and Inventory

Inventory:
```
get <item>              # Pick up
get all
drop <item>
put <item> <container>
get <item> <container>
```

Equipment:
```
wear <item> | wear all
remove <item> | remove all
wield <weapon>
hold <item>
```

Item info:
```
examine <item>
identify <item>         # Via spell/service
compare <item1> <item2>
donate <item>
sacrifice <item>
```

Shopping:
```
list
buy <item>
sell <item>
value <item>
browse
```

## Character Development

Advancement:
```
experience | exp       # Show XP
level                  # Check readiness
train                  # Increase stats (at trainer)
gain                   # Level up when ready (at guild master)
```

Info and customization:
```
score
attribute
affects
title <new title>
description
alias <name> <command> | alias | unalias
color
prompt
```

## Advanced Systems

Associations (guilds):
```
who association
association
atalk <message>
```

PvP:
```
pk
consider <player>
hunt <player>          # In PvP zones and if tracking is enabled
```

Contracts/quests:
```
contracts
contract <number>
complete <contract>
quest
```

Banking:
```
balance
deposit <amount>
withdraw <amount>
exchange
```

Housing:
```
house
house buy
house enter
house guests
```

Ships:
```
board <ship>
sail <direction>
anchor
disembark
```

Mail:
```
mail <player>
receive
```

Auction:
```
auction
bid <amount>
auction <item> <minimum>
```

Note: Availability of some systems depends on server configuration and installed areas. Consult in-game help.

## Tips

- Save often (`save`).
- Read help files (`help <topic>`).
- Ask on newbie/help channels.
- Explore level-appropriate areas and group when possible.
- Repair and maintain gear.
- Rest to recover HP/Mana/Move.

## Abbreviations

AC (Armor Class), HP (Hit Points), XP/EXP (Experience), GP (Gold Pieces),
MOB (Mobile/NPC), PC (Player Character), PK (Player Kill), AFK, OOC, IC,
THAC0 (To Hit Armor Class 0).

## Help

- In-game help: `help <topic>`
- Newbie channel for quick questions
- Look for helper players in WHO list
- City bulletin boards
- Refer to server website/forums if provided
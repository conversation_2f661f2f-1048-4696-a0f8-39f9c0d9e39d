# Admin/Wizard Guide

Administration and immortal operations for Outcast MUD staff. This version aligns with the current codebase and build (Makefile outputs ocm_new; zlib is linked; MySQL disabled by default).

## Table of Contents

1. [Immortal Levels](#immortal-levels)
2. [Basic Immortal Commands](#basic-immortal-commands)
3. [Player Management](#player-management)
4. [World Management](#world-management)
5. [Area Building Commands](#area-building-commands)
6. [System Administration](#system-administration)
7. [OLC (Online Creation)](#olc-online-creation)
8. [Debugging and Monitoring](#debugging-and-monitoring)
9. [Special Procedures](#special-procedures)
10. [Best Practices](#best-practices)
11. [Emergency Procedures](#emergency-procedures)
12. [Common Tasks](#common-tasks)

## Immortal Levels

Typical hierarchy (exact ranges may vary by server config):

- 51–53: Builder/Helper
- 54–56: Immortal/Wizard
- 57–58: Senior Wizard
- 59: Administrator
- 60: Implementor

Higher levels unlock progressively powerful commands.

## Basic Immortal Commands

### Visibility and Movement

```
invis [level]                 # Set invisibility
vis                           # Become visible
goto <room|player|mob>        # Teleport to target
trans <target>                # Bring target to you
teleport <target> <room>      # Move target elsewhere
```

### Information

```
stat <target>                 # Detailed info (room/mob/obj/player)
vnum <type> <keywords>        # Search vnums
where [target]                # Locate entities
users                         # WHO with IPs
last <player>                 # Last login info
finger <player>               # Player info
```

### Communication

```
wiznet <msg>                  # Immortal channel
: <msg>                       # Wiznet shortcut
echo/gecho/zecho <msg>        # Room/Global/Zone echo
send <player> <msg>           # Private message
```

## Player Management

### Character Modification

```
set <player> <field> <value>
# Examples:
set <player> level <1-60>
set <player> gold <amount>
set <player> str <3-25>
set <player> hp <amount>
set <player> maxhp <amount>
set <player> exp <amount>
set <player> align <-1000..1000>
set <player> title <new title>
set <player> frozen on|off
```

### Control and Discipline

```
freeze/thaw <player>
mute/unmute <player>
notitle <player>
jail/pardon <player>
ban/unban/siteok <site>
password <player> <new>
delete/undelete <player>
```

### Assistance

```
restore/heal <player>
advance <player> <level>
skillset <player> <skill> <percent>
spellset <player> <spell>
load obj <vnum>
give <obj> <player>
purge <target>
```

## World Management

### Rooms

```
rstat [room#]
rvnum <keywords>
goto <room#>
rat <room#> <command>
```

### Mobiles

```
mstat <mobile>
mvnum <keywords>
mload <vnum>
mpurge <mobile>
mat <mob> <command>
```

### Objects

```
ostat <object>
ovnum <keywords>
oload <vnum>
opurge <object>
```

### Zones

```
zstat <zone#>
zreset <zone#>
zedit <zone#>
show zones
```

## Area Building Commands

### Rooms

```
redit [room#]                 # Edit current/specific room
rclone <src> <dst>
rdelete <room#>
rlink <dir> <room#>
```

### Mobiles

```
medit <vnum>
mclone <src> <dst>
mdelete <vnum>
```

### Objects

```
oedit <vnum>
oclone <src> <dst>
odelete <vnum>
```

### Utilities

```
dig <dir> <new room#>
saveall
alist
asave <area>
```

## System Administration

### Server Control

```
shutdown <time>|cancel
reboot <time>
reload <type>
copyover
```

Note: For OS-level service management, see installation docs (systemd starts ocm_new).

### Files and Logs

```
file <filename>
syslog
changelog
errors
saves
```

### Configuration

```
show <option>
toggle <option>
config
wizlock on|off
newlock on|off
```

## OLC (Online Creation)

```
olc                         # Enter OLC
oedit/redit/medit/zedit     # Edit modes
show                        # Show buffer
done                        # Exit OLC
save                        # Save to memory
asave                       # Persist to disk
```

Room fields typically include: name, desc, sector, flags, exits.

## Debugging and Monitoring

### Monitoring

```
uptime
memory
connections
loops
performance
```

### Debug

```
debug <level>
log <type>
snoop <player> / unsnoop
switch <mobile> / return
```

### Testing

```
damage <target> <amount>
peace
force <target> <command>
lag <player> <seconds>
```

## Special Procedures

### Events

```
event list | create <type> | cancel <id> | modify <id> <params>
```

### Quests

```
quest list | start <id> | end <id> | reset <player>
```

### Associations

```
assoc list
assoc members <name>
assoc add <player> <assoc>
assoc remove <player>
assoc leader <player>
```

## Best Practices

- Log impactful actions and changes.
- Test in a non-production port/instance.
- Back up areas before edits (asave regularly).
- Coordinate on wiznet; avoid revealing immortal identity.
- Help players without granting unfair advantages.
- Record and report bugs with steps to reproduce.

## Emergency Procedures

### Crash

1) Review lib/logs/boot.log and errors.
2) Verify Players/ files.
3) Run integrity checks (area links, zone loads).
4) Restore from backup if necessary.
5) Document details for postmortem.

### Player Incidents

- Investigate, collect logs, consult seniors, apply consistent actions, log outcomes.

### Security

- Rotate passwords, check unauthorized access, review logs, ban compromised sources, notify admins.

## Common Tasks

### New Player Assistance

```
goto <newbie>
vis
say Welcome to Outcast MUD!
restore <newbie>
load obj <starter vnum>
give <item> <newbie>
```

### Area Smoke Test

```
goto <test room>
mload <test mob>
oload <test item>
force <mob> kill <me>
peace
stat room
```

### Reimbursements

```
finger <player>
goto <player>
load obj <vnum>
give <item> <player>
send <player> Item restored due to <reason>
```

Notes:
- Some commands vary by distribution; consult in-game help for exact syntax.
- Use minimal required privileges and prefer reversible actions.
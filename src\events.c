/* ***************************************************************************
 *  File: events.c                                             Part of Outcast *
 *  Usage: manipulate various event lists                                    *
 *  Copyright  1994, 1995 - <PERSON> and Outcast Systems Ltd.             *
 *************************************************************************** */

#include <string.h>
#include <stdio.h>
#ifndef _LINUX_SOURCE
   #include <sys/types.h>
#endif

#include "comm.h"
#include "db.h"
#include "events.h"
#include "prototypes.h"
#include "specs.include.h"
#include "specs.prototypes.h"
#include "spells.h"
#include "race_class.h"
#include "utils.h"
#ifdef NEWJUSTICE
   #include "newjustice.h"
#endif
#ifdef <PERSON>TI<PERSON>
   #include "justice.h"
#endif
#include "mm.h"
#include "path.h"

unsigned long rescheduleCounter = 0;
void release_mob_mem(P_char ch);

/* global list of pointers to all pending event structures. */

P_event event_list = 0;
P_event event_list_tail = 0;

/* event structs are never freed (currently), they are created as needed, and added to <event_list>, <schedule>,
   and <event_type_list>.  Once triggered, if they are not cyclical, they are removed from these 3 lists and added
   to the head of <avail_events>.  No processing is done on <avail_events> it is merely a 'holding' list for
   unused event elements, when a new event is needed, it's pulled off this list, rather than having to create/free
   them all the time. */

P_event avail_events = 0;

/* main array of pointers to lists, schedule is the 'master' controller, has one element per pulse in a
   real minute.  */

P_event schedule[240];
P_event schedule_tail[240];

/* event_loading holds the number of pending events for each pulse, used by ReSchedule to balance the loading of
   events so we don't get everything happening at once. */

u_int event_loading[240];

/* event_type_list is another array of pointers, but has one element per
   EVENT_*, useful for finding an existing event by type. */

P_event event_type_list[LAST_EVENT];
P_event event_type_list_tail[LAST_EVENT];

/* event_counter is a diagnostic tool more than anything else, each element of this array holds the total number
   of pending events of that type.  event_counter[LAST_EVENT] holds the number of elements in event_list (ie. all
   pending events).  event_counter[LAST_EVENT + 1] holds the number of elements in avail_events (ie. how many
   event elements are sitting around taking up space and not doing anything.) */

u_int event_counter[LAST_EVENT + 2];

/* global var that holds the event currently being triggered, or NULL. */

P_event current_event = 0;

/* names of event types, used in log messages and by 'world events' */

const char *event_names[] =
{
   "NULL",
   "Deferred",
   "Wait",
   "Regen Hits",
   "Regen Moves",
   "Regen Mana",
   "Mob Mundane",
   "Mob Special",
   "Object",
   "Room",
   "Falling Char",
   "Falling Obj",
   "Zone Reset",
   "Decay",
   "Skill Reset",
   "Special",
   "Ship Move",
   "Fire Plane",
   "Auto Save",
   "Shout",
   "Spell Scribe",
   "Spell Mem",
   "Spellcast",
   "Melee combat",
   "Bard singing",
   "Bard fx-decay",
   "Delayed func",
   "Stunned",
   "AggAttack",
   "Berserk",
   "Affect Bal",
   "Hunter",
   "Obj Timer",
   "Room Timer",
   "NPC Timer",
   "Brain Draining",
   "&+RUnused 1&n",
   "Room Exec Fn",
   "Object Execute Fn",
   "Corpse Save",
   "Stringing",
   "Burning",
   "Disguise",
   "Savable Char Exec",
   "Proc Periodic",
   "Myconid emitting",
   "Myconid fx-decay",
   "Function Exec",
   "Justice Debit",
   "Path Init",
   "Path",
   "Path Delayed Command",
   "Bard Song Timer",
   "Tendrils Removed",
   ""
};

/* The way this works:

   There are only 2 lists of struct event_data elements, event_list, and avail_events, event_list holds the events
   that are pending, avail_events holds the elements that are not currently in use.  event_list is doubly-linked,
   avail_events is a FIFO stack.

   schedule[] is 240 lists of pointers to events (one for each pulse in a real minute), elements in these lists
   are also doubly-linked.

   event_type_list[] is (LAST_EVENT) lists of pointers to events, also doubly-linked to facilitate finding an
   event of a given type.

   current_event is just a pointer to the 'active' event, or NULL, if event processing is not being done.  Can be
   used as a flag to see if current call is the result of an event or not.

   For almost ALL purposes, the only things you need to know is what the various EVENT_ types are, and what
   arguments to give the AddEvent() macro.

   schedule array is used to segregate event timing, as pulses are advanced, they step down this array, each index
   represents a 1/4 second timing pulse.  If an event is supposed to take place 30 minutes in the future, we add an
   event_data element to the list pointed to by schedule[pulse], with a timer value of 30.  Then every 240th pulse,
   the timer value of all events in that element's list are decremented, when they hit 0, they are triggered (and
   deleted from the schedule list if one_shot is TRUE).  Periodic events will just reset the timer value (things
   like the clock tower in WD spring to mind) which is why current_event exists (and will always point to the
   active event).  All specials (mob/obj/room/global) will use this event queue.

   Advantages to doing this?  Speed.  Flexibility.  Efficiency.  It eliminates LOTS of redundant and repetitious
   processing.  Why speed up something that's already damn fast?  Because it frees up MANY processor cycles we can
   use creatively (want to set a bunch of guards patrolling an area with a fixed schedule?  Want a spell effect
   delayed by 10 real minutes?  Want combat blow timing to be settable based on character's speed and condition?
   Want to have regen rates set so that they get one point at a time, at various times, rather than everyone
   getting a bunch on the tick, every tick?  No problem.

   Key to this working, list of EVENT_TYPEs, there is room for (ubyte)255 different TYPES of events, that should
   be more than enough for any foreseeable future.

   event_type_list has the list of pending events, (by event type) avail_events holds the list of allocated, but
   not pending event elements.  When a one_shot event is triggered and removed, it's added to the head of this
   list.  When a new event needs to be added, it checks this list and reuses the elements, only if no unused event
   elements are available does it allocate a new one.  No mechanism for freeing elements on these lists currently,
   if this becomes a problem, I'll add an event to periodically free excess event elements.
 */

struct mm_ds *dead_event_pool = NULL;

/* external variables */

extern int port;
extern P_index mob_index;
extern P_index obj_index;
extern P_obj object_list;
extern P_room world;
extern const struct trait_data racial_traits[];
extern int pulse;
extern int top_of_mobt;
extern int top_of_objt;
extern int top_of_world;
extern int top_of_zone_table;
extern struct time_info_data time_info;
extern struct zone_data *zone;
extern struct zone_data *zone_table;

/* remove all events associated with the target char.  Called from
   extract_char so that we don't wind up with bogus pointers for events.  */

void ClearCharEvents(P_char target)
{
   P_event e1 = NULL, e2 = NULL;

   if(!target)
      {
      logit(LOG_DEBUG, "ClearCharEvents called with NULL target");
      return;
      }
   /* ok, minor mod here, if a char dies, as a RESULT of event processing, it is barely possible to whack up the
      event list.  So, to prevent this, we detach all the events from the (soon to be missing) char struct, change
      the type of all of them to EVENT_NONE, and carry on, later event processing will take care of removing the
      events themselves.  JAB */


   for(e1 = target->events; e1; e1 = e2)
      {
      e2 = e1->next;

      if(e1->type == EVENT_NONE)
         continue;

      /* first, free t_arg if needed */

      switch(e1->type)
         {
         case EVENT_BURN_CHAR:
            REMOVE_CBIT(e1->actor.a_ch->specials.affects, AFF_BURNING);
            break;
         case EVENT_MULTI_ROUND_SPELL:
            REMOVE_CBIT(e1->actor.a_ch->specials.affects, AFF_MULTI_ROUND_SPELL);
            break;
         case EVENT_BERSERK:
            if(affected_by_spell(target, SKILL_BERSERK))
               affect_from_char(target, SKILL_BERSERK);
            break;
         case EVENT_DELAYED_COMMAND:
         case EVENT_PATH_DELAY:
         case EVENT_MOB_MUNDANE:
         case EVENT_MOB_SPECIAL:
         case EVENT_NPC_TIMER:
         case EVENT_SPECIAL:
            if(e1->target.t_arg)
               {
               free_string((char *) e1->target.t_arg);
               e1->target.t_arg = NULL;
               }
            break;
         case EVENT_STUNNED:
            REMOVE_CBIT(e1->actor.a_ch->specials.affects, AFF_STUNNED);
            break;
         case EVENT_DISGUISE:
            disguiseEvent(target, TRUE);
            break;
         case EVENT_MOB_HUNT:
            if(e1->target.t_hunt)
               {
               free((char *) e1->target.t_hunt);
               e1->target.t_hunt = NULL;
               }
            break;
         }

      /* remove from 'current' event_type_list[] list */
      if(event_type_list[e1->type] == e1)
         {
         event_type_list[e1->type] = e1->next_type;
         if(event_type_list[e1->type])
            event_type_list[e1->type]->prev_type = NULL;
         }
      else
         {
         e1->prev_type->next_type = e1->next_type;
         if(e1->next_type)
            e1->next_type->prev_type = e1->prev_type;
         }

      if(event_type_list_tail[e1->type] == e1)
         event_type_list_tail[e1->type] = e1->prev_type;

      /* and add it to 'EVENT_NONE' event_type_list[] list */
      if(!event_type_list[EVENT_NONE])
         {
         event_type_list[EVENT_NONE] = e1;
         e1->prev_type = NULL;
         }
      else
         {
         event_type_list_tail[EVENT_NONE]->next_type = e1;
         e1->prev_type = event_type_list_tail[EVENT_NONE];
         }

      event_type_list_tail[EVENT_NONE] = e1;

      /* update counters */
      event_counter[e1->type]--;
      event_counter[EVENT_NONE]++;

      /* and set things for minimal impact */
      e1->one_shot = TRUE;
      e1->timer = 1;
      e1->type = EVENT_NONE;
      e1->next = NULL;
      e1->next_type = NULL;
      }

   target->events = NULL;
}

/* remove all events associated with the target object.  Called from
   extract_obj so that we don't wind up with bogus pointers for events.  */

void ClearObjEvents(P_obj target)
{
   P_event e1 = NULL, e2 = NULL, e3 = NULL;

   if(!target)
      {
      logit(LOG_DEBUG, "ClearObjEvents called with NULL target");
      return;
      }

   if(current_event && (current_event->actor.a_obj == target))
      current_event = NULL;

   e3 = current_event;
   for(e1 = target->events; e1; e1 = e2)
      {
      e2 = e1->next;
      current_event = e1;
      RemoveEvent();
      }

   current_event = e3;
}

/* check/add a regen event for ch (currently just hits/moves */
void StartRegen(P_char ch, int type)
{
   int i, j = 1, k = 0;
   P_event e1 = NULL;
   bool null_regen = FALSE;

   if(!ch)
      {
      logit(LOG_DEBUG, "SR: NULL char");
      return;
      }

   if(ch->in_room == NOWHERE)
      return;

   if((type != EVENT_HIT_REGEN) && (type != EVENT_MOVE_REGEN) && (type != EVENT_MANA_REGEN))
      {
      logit(LOG_DEBUG, "SR: Illegal type %d. ", type);
      return;
      }

   for(e1 = ch->events; e1; e1 = e1->next)
      {
      if((e1->type == type) && (e1->timer > 0))
         return;
      }

   switch(type)
      {
      case EVENT_HIT_REGEN:
         i = hit_regen(ch);
         break;
      case EVENT_MOVE_REGEN:
         i = move_regen(ch);
         break;
      case EVENT_MANA_REGEN:
         i = mana_regen(ch);
         break;
      default:
         return;
         break;
      }

   if(!i && !e1)
      return;

   if(i < 0)
      {
      k = 1;
      i = -i;
      }
   else
      {
      switch(type)
         {
         case EVENT_HIT_REGEN:
            if(GET_HIT(ch) >= GET_MAX_HIT(ch))
               {
               if(e1 && (e1 == current_event))
                  null_regen = TRUE;
               else
                  return;
               }
            break;
         case EVENT_MOVE_REGEN:
            if(GET_MOVE(ch) >= GET_MAX_MOVE(ch))
               {
               if(e1 && (e1 == current_event))
                  null_regen = TRUE;
               else
                  return;
               }
            break;
         case EVENT_MANA_REGEN:
            if(GET_MANA(ch) >= GET_MAX_MANA(ch))
               {
               if(e1 && (e1 == current_event))
                  null_regen = TRUE;
               else
                  return;
               }
            break;
         }
      }

   if(i > 240)
      {
      while(i > 240)
         {
         j++;
         i -= 240;
         }

      i = 1;
      }
   else
      i = MAX(1, ((240 / i) + number(0, 1)));

   /* reason for this, after they have regenned we add a final regen event with 0 value, so that they don't
      continually regen 1 point.  Before, last regen event wore off, then they would immediately be able to add
      another one (primarily on moves, with frequent small subtractions) */

   if(null_regen)
      j = 0;

   /* Slow regen rate to 1/3 for low-level mobs */
   if((IS_NPC(ch)) && (!IS_PET(ch)) && (GET_LEVEL(ch) <= 10))
      i *= 3;

   AddEvent(type, i, TRUE, ch, (k) ? -j : j);
}

/* replacement for WAIT_STATE, uses the EVENT_COMMAND_WAIT events and the
   ch->delay_commands flag to control command entry rate. */
void CharWait(P_char ch, int delay)
{
   P_event e1 = NULL, e2 = NULL;
   int i;

   if(ch->specials.command_delays)
      {
      for(e1 = ch->events; e1; e1 = e1->next)
         {
         if(e1->type == EVENT_COMMAND_WAIT)
            break;
         }

      if(e1)
         {
         i = (e1->timer - 1) * 240;
         if(e1->element >= pulse)
            i += (e1->element - pulse);
         else
            i += e1->element + (239 - pulse);

         if(i > delay)
            return;

         e2 = current_event;
         current_event = e1;
         RemoveEvent();
         current_event = e2;
         }
      else
         dump_core();   // bad, evil code, this should never happen, if it does, blow up so we can fix it. JAB
      }

   if(IS_TRUSTED(ch))
      {
      ch->specials.command_delays = FALSE;
      return;
      }

   if(AddEvent(EVENT_COMMAND_WAIT, delay, TRUE, ch, 0))
      ch->specials.command_delays = TRUE;
   else
      dump_core();
}


/* Provides a centralized way of checking if a mob can be stunned - Iyachtu */
/* Returns TRUE if mob is stunnable, FALSE if not */
int CheckStun(P_char ch)
{
   if((GET_RACE(ch) == RACE_DRAGON) || (GET_RACE(ch) == RACE_DEMON) ||
      (GET_RACE(ch) == RACE_DEVIL) || (GET_RACE(ch) == RACE_ANGEL) ||
      (GET_RACE(ch) == RACE_GOLEM) || IMMATERIAL(ch))
      return FALSE;
   else
      return TRUE;
}


/* adds Stun affect to ch, for duration, stun is cumulative. JAB */

void Stun(P_char ch, int duration)
{
   P_event e1 = NULL, e2 = NULL;

   if(STUN_PROOF(ch))
      return;

   for(e1 = ch->events; e1; e1 = e1->next)
      {
      if(e1->type == EVENT_STUNNED)
         break;
      }

   if(e1)
      {
      /* add existing stun time to new time, delete old event */
      duration += ((e1->timer - 1) * 240 + e1->element - pulse + ((e1->element < pulse) ? 240 : 0));
      e2 = current_event;
      current_event = e1;
      RemoveEvent();
      current_event = e2;
      send_to_char("The world spins faster, and is that 'The Anvil Chorus?'\n", ch);
      }
   else
      {
      send_to_char("The world starts spinning, and your ears are ringing!\n", ch);
      act("$n is stunned!", TRUE, ch, 0, 0, TO_ROOM);
      }

   if(IS_SINGING(ch))
      bard_stop_singing(ch);

   SET_CBIT(ch->specials.affects, AFF_STUNNED);
   AddEvent(EVENT_STUNNED, duration, TRUE, ch, 0);
}

/* entering a room sets a delayed agg attack for elegible movers and occupants, when that event triggers, this
   routine gets called to see if target is still legal, if it is, it starts combat.  JAB */
void AggAttack(void)
{
   P_char ch, victim;

   if(!current_event || (current_event->element == 255U))
      {
      logit(LOG_EXIT, "no current_event in call to AggAttack()");
      dump_core();
      }

   ch = current_event->actor.a_ch;
   victim = current_event->target.t_ch;

   if((victim->in_room == NOWHERE) || (GET_STAT(victim) == STAT_DEAD) || (ch->in_room == NOWHERE) ||
      !MIN_POS(ch, POS_STANDING + STAT_RESTING) || !is_aggr_to(ch, victim) || IS_FIGHTING(ch))
      return;

   if(ch->in_room == victim->in_room)
      {
      send_to_char("Being the ferocious sort, you charge at the enemy!\n", ch);

      if(IS_NPC(ch))
         MobStartFight(ch, victim);
      else
         {
         if((GET_CHAR_SKILL(ch, SKILL_BACKSTAB) > 0) &&
            ((ch->equipment[WIELD] && (ch->equipment[WIELD]->value[3] == 11)) ||
             (ch->equipment[SECONDARY_WEAPON] &&
              (ch->equipment[SECONDARY_WEAPON]->value[3] == 11))))
            backstab(ch, victim);
         else
            attack(ch, victim);
         }
      }
   else
      {
      /* They moved before event triggered, let's see if they are nearby */
      if(IS_PC(ch) || IS_NPC(victim))
         return; // PCs will have to track on their own

      if(IS_CSET(ch->only.npc->npcact, ACT_HUNTER) && HAS_MEMORY(ch))
         {
         mem_addToMemory(ch->only.npc->memory, GET_NAME(victim));
         InitNewMobHunt(ch);
         }
      }
}

/* For thief skill disguise, and illusionists spells.  Checks disguise */
/* and either reassigns the event or removes the disguise              */
/* - 12/98 CRM                                                         */
void disguiseEvent(P_char ch, int flag)
{
   P_event e1 = NULL, e2;
   int ok = FALSE, result = 0, real_flag;
   int next_time;

   real_flag = flag;

   debuglog(51, DS_DIGUISE, "Entering disguiseEvent, flag = %d", flag);

   for(e1 = ch->events; e1; e1 = e1->next)
      {
      if(e1->type == EVENT_DISGUISE)
         break;
      }

   if(current_event && e1 && (current_event == e1))
      result = 3;   /* remove magical disguise */
   else if(!e1 && IS_DISGUISE(ch))
      result = 2;   /* remove disguise */
   else if(flag == 1)
      result = 2;   /* remove physical disguise */
   else if(flag == 2)
      result = 3;   /* remove magical disguise (illusion) */
   else
      result = 1;

   switch(result)
      {
      case 1:
         if(!IS_DISGUISE(ch))
            break;

         if(number(1,75) < GET_LEVEL(ch))
            ok = TRUE;

         if(ok || IS_TRUSTED(ch))
            {
            if(number(1,3) == 2)
               next_time = 600 + number(1,200);
            else
               next_time = 600 - number(1,200);

            AddEvent(EVENT_DISGUISE, next_time, TRUE, ch, 0);
            }
         else
            {
            IS_DISGUISE_PC(ch) = FALSE;
            IS_DISGUISE_NPC(ch) = FALSE;
            ch->disguise.name = NULL;
            ch->disguise.class = 0;
            ch->disguise.race = 0;
            ch->disguise.level = 0;
            ch->disguise.title = NULL;

            send_to_char("Your disguise falls apart!\r\n", ch);
            act("$n's disguise falls apart!", FALSE, ch, 0, 0, TO_ROOM);
#ifdef NEWJUSTICE
            //      justice_witness(ch, NULL, CRIME_DISGUISE);
#endif
            }

         break;

      case 2: // Physical disguise
         if(IS_DISGUISE(ch))
            {
            IS_DISGUISE_PC(ch) = FALSE;
            IS_DISGUISE_NPC(ch) = FALSE;
            ch->disguise.name = NULL;
            ch->disguise.class = 0;
            ch->disguise.race = 0;
            ch->disguise.level = 0;
            ch->disguise.title = NULL;

            send_to_char("Your disguise falls apart!\r\n", ch);
            act("$n's disguise falls apart!", FALSE, ch, 0, 0, TO_ROOM);
#ifdef NEWJUSTICE
            //      justice_witness(ch, NULL, CRIME_DISGUISE);
#endif
            }
         break;

      case 3: // Magical disguise
         if(IS_DISGUISE(ch))
            {
            IS_DISGUISE_PC(ch) = FALSE;
            IS_DISGUISE_NPC(ch) = FALSE;
            ch->disguise.name = NULL;
            ch->disguise.class = 0;
            ch->disguise.race = 0;
            ch->disguise.level = 0;
            ch->disguise.title = NULL;

            send_to_char("Your disguised form shimmers briefly and returns to normal.\n", ch);
            act("$n is revealed as $s illusory disguise fades away!", FALSE, ch, 0, 0, TO_ROOM);
#ifdef NEWJUSTICE
            //      justice_witness(ch, NULL, CRIME_DISGUISE);
#endif
            }

         break;
      }

   if(e1)
      {
      e2 = current_event;
      current_event = e1;
      RemoveEvent();
      current_event = e2;
      }
}

/* Start/Stop Berserkering.  JAB */
void Berserk(P_char ch, int flag, int calledBy)
{
   P_event e1 = NULL, e2;
   int time_left = 0, result = 0;
   struct affected_type af;

   for(e1 = ch->events; e1; e1 = e1->next)
      {
      if(e1->type == EVENT_BERSERK)
         break;
      }

   /* ok, this routine called from 4 places, do_berserk to start/stop, perform_violence to start, Events to stop,
      and stop_fighting to stop, all but the Events call are handled the same, the Events call stops it, period,
      the other three require a skill check. */

   /* handle the Events() call */
   if(current_event && e1 && (current_event == e1))
      result = 2;                 /* flag for stopping */
   else if(!e1 && affected_by_spell(ch, SKILL_BERSERK))
      result = 2;                 /* lost our berserk event somehow, stop it to be safe */
   else
      {
      if(calledBy != SKILL_BATTLE_TRANCE)
         {
         if(!GET_CHAR_SKILL(ch, SKILL_BERSERK))
            return;
         }

      if(e1)
         time_left = event_time(e1, T_SECS);

      if(flag)
         { /* trying to start berserking */
         if(IS_STUNNED(ch))
            return;                 /* no starting to berserk while stunned */

         if(e1)
            result = 0;             /* trying to start, but already are, no effect */
         else
            {
            /* trying to start a berserk */
            if(calledBy == SKILL_BATTLE_TRANCE)
               result = 1;
            else
               {
               if(number(1, (150 - (IS_FIGHTING(ch) ? 30 : 0))) < (GET_LEVEL(ch) + GET_CHAR_SKILL(ch, SKILL_BERSERK)))
                  result = 1;         /* success */
               else
                  result = 0;         /* failure */
               }
            }
         }
      else
         { /* trying to stop berserking */
         if(!e1)
            result = 0;             /* they aren't berserk, ignore */
         else
            {
            if(number(1, (150 + (IS_FIGHTING(ch) ? 60 : (PickTarget(ch) ? 25 : 0)) + time_left)) < (GET_LEVEL(ch) + GET_CHAR_SKILL(ch, SKILL_BERSERK)))
               result = 2;           /* success */
            else
               result = 0;           /* failure */
            }
         }
      }

   switch(result)
      {
      case 2:                       /* stop berserking */
         affect_from_char(ch, SKILL_BERSERK);
         send_to_char("Your blood cools, and you no longer see targets everywhere.\n", ch);
         act("$n seems to have overcome $s battle madness.", TRUE, ch, 0, 0, TO_ROOM);
         time_left = 4000 - dice(GET_LEVEL(ch), GET_CHAR_SKILL(ch, SKILL_BERSERK));

         /* time_left will vary from -900 to about 3995, if it's > 240 stun for
            time_left / 40 pulses (6 - 99 pulses which is .5 to 8 rounds) */
         if((time_left > 239) && !affected_by_spell(ch, SKILL_BATTLE_TRANCE))
            Stun(ch, time_left / 40);

         if(e1)
            {
            e2 = current_event;
            current_event = e1;
            RemoveEvent();
            current_event = e2;
            }
         break;

      case 1:                       /* start berserking */
         act("$n looks FURIOUS and goes into a mindless rage!!",
             FALSE, ch, 0, 0, TO_ROOM);

         /* Msg doesnt make sense if called from do_battle_trance */
         if(calledBy != SKILL_BATTLE_TRANCE)
            send_to_char("Your instincts force you into a mindless rage!\n", ch);

         /* both low skill and low level act to increase duration, at the same time they act to make it less likely.
            (IE:  low skill and low level are real unlikely to berserk, but when they do, it's gonna last a long time, a
            high skill/level indicates more control, so it happens more often, but for shorter duration, and is easier
            to stop.) */

         time_left = MAX(24, (160 - GET_CHAR_SKILL(ch, SKILL_BERSERK) - GET_LEVEL(ch)));

         /* 24 to about 150 pulses (2 to 12 rounds) */
         AddEvent(EVENT_BERSERK, time_left, TRUE, ch, 0);

         bzero(&af, sizeof(af));
         af.type = SKILL_BERSERK;
         af.duration = 99;
         af.modifier = ch->base_stats.Str;
         af.location = APPLY_STR_MAX;
         affect_to_char(ch, &af);

         af.modifier = ch->base_stats.Con;
         af.location = APPLY_CON_MAX;
         affect_to_char(ch, &af);

         af.modifier = -(ch->base_stats.Int * 2 / 3);
         af.location = APPLY_INT;
         affect_to_char(ch, &af);

         af.modifier = -(ch->base_stats.Wis * 2 / 3);
         af.location = APPLY_WIS;
         affect_to_char(ch, &af);

         AddEvent(EVENT_BALANCE_AFFECTS, 1, TRUE, ch, 0);
         break;
      }
}

/* return the time left in the e1 event, various units (see events.h) */
int event_time(P_event e1, int ttype)
{
   int time_left;

   if(!e1 || (ttype < T_PULSES) || (ttype > T_DAYS))
      return 0;

   time_left = (e1->timer - 1) * 240 + e1->element - pulse;
   if(e1->element < pulse)
      time_left += 240;

   switch(ttype)
      {
      case T_PULSES:
         return time_left;
         break;
      case T_SECS:
         return(time_left / WAIT_SEC);
         break;
      case T_ROUNDS:
         return(time_left / WAIT_ROUND);
         break;
      case T_MINS:
         return(time_left / WAIT_SEC / SECS_PER_REAL_MIN);
         break;
      case T_HOURS:
         return(time_left / WAIT_SEC / SECS_PER_REAL_HOUR);
         break;
      case T_DAYS:
         return(time_left / WAIT_SEC / SECS_PER_REAL_DAY);
         break;
      }

   return time_left;
}

/* balance the load by shifting an event from a heavily loaded pulse to a more lightly loaded pulse.  Simpler
   because we only have to worry about the schedule[] list, the others are unchanged.  Called once per minute and
   will reschedule 0-240 events.  240 will be very very very rare, and will only happen under extremely odd
   circumstances. */
void ReSchedule(void)
{
   P_event e1 = NULL;
   ubyte i, i2, counter = 0;

   for(i = 0; i < 240; i++)
      {
      if(i != 239)
         i2 = i + 1;
      else
         i2 = 0;

      if(schedule_tail[i] && schedule_tail[i]->type == EVENT_SPELLCAST)
         continue;

      if(event_loading[i] && (event_loading[i] > (event_loading[i2] + 2)))
         {
         /* yup, there are at least 3 more events under [i] than [i2], shift last
         scheduled down one slot.  And update event_loading counters. */

         e1 = schedule_tail[i];
         e1->element = i2;
         counter++;

         schedule_tail[i] = schedule_tail[i]->prev_sched;
         if(schedule_tail[i])
            schedule_tail[i]->next_sched = NULL;

         if(!schedule[i2])
            {
            schedule[i2] = schedule_tail[i2] = e1;
            e1->prev_sched = NULL;
            }
         else
            {
            e1->prev_sched = schedule_tail[i2];
            schedule_tail[i2]->next_sched = e1;
            schedule_tail[i2] = e1;
            }

         event_loading[i]--;
         event_loading[i2]++;
         }
      }
}

/* remove current_event from event arrays, returns TRUE on success */
bool RemoveEvent(void)
{
   int   mvnum, rvnum;
   P_event e1 = current_event, e2;

   if(!e1 || (e1->element == (ubyte) 255))
      dump_core();

   /* first, free t_arg if needed */

   switch(e1->type)
      {
      case EVENT_DELAYED_COMMAND:
      case EVENT_PATH_DELAY:
      case EVENT_MOB_MUNDANE:
      case EVENT_MOB_SPECIAL:
      case EVENT_NPC_TIMER:
      case EVENT_OBJ_SPECIAL:
      case EVENT_OBJ_TIMER:
      case EVENT_ROOM_SPECIAL:
      case EVENT_ROOM_TIMER:
      case EVENT_SPECIAL:
      case EVENT_PATH_INIT:
         if(e1->target.t_arg)
            {
            free_string((char *) e1->target.t_arg);
            e1->target.t_arg = NULL;
            }
         break;
      case EVENT_ROOM_EXECUTE:
         if(e1->target.t_r_e_d)
            free((char *)e1->target.t_r_e_d);
         e1->target.t_r_e_d = NULL;
         break;
      case EVENT_STUNNED:
         REMOVE_CBIT(e1->actor.a_ch->specials.affects, AFF_STUNNED);
         break;
      case EVENT_SPELLCAST:
#if 0
         nuke_spellcast(e1->target.t_spell);
#endif
         e1->target.t_spell = NULL;
         break;
      case EVENT_MOB_HUNT:
         if(e1->target.t_hunt)
            {
            free((char *) e1->target.t_hunt);
            e1->target.t_hunt = NULL;
            }
         break;
#ifdef EVENT_SAVING
      case EVENT_JUSTICE_DEBT:
      case EVENT_SCHAR_EXECUTE:
         /* free the user data space and t_sced struct allocated for the target of this event */
         /* the event itself is extracted from the char by the next switch */
         if(e1->target.t_sced)
            {
            if(e1->target.t_sced->t_arg && e1->target.t_sced->t_arg_size == 0)
               {
               logit(LOG_EXIT, "Found a EVENT_SCHAR_EXECUTE with allocated user data space and zero t_arg_size");
               dump_core();
               }

            if(e1->target.t_sced->t_arg)
               free (e1->target.t_sced->t_arg);

            free (e1->target.t_sced);
            e1->target.t_sced = NULL;
            }
         break;
#endif
      case EVENT_VOID_FN_EXECUTE:
         if(e1->target.t_param)
            {
            free_string((char *) e1->target.t_param);
            e1->target.t_param = NULL;
            }
         break;
      }

   /* remove from obj or char first */

   switch(e1->type)
      {
      case EVENT_AGG_ATTACK:
      case EVENT_AUTO_SAVE:
      case EVENT_BALANCE_AFFECTS:
      case EVENT_BARD_FX_DECAY:
      case EVENT_BARD_SINGING:
      case EVENT_BERSERK:
      case EVENT_BRAIN_DRAINING:
      case EVENT_CHAR_EXECUTE:
      case EVENT_BARD_EXECUTE:
#ifdef EVENT_SAVING
      case EVENT_JUSTICE_DEBT:
      case EVENT_SCHAR_EXECUTE:
#endif
      case EVENT_COMBAT:
      case EVENT_COMMAND_WAIT:
      case EVENT_DELAYED_COMMAND:
      case EVENT_DISGUISE:
      case EVENT_FALLING_CHAR:
      case EVENT_HIT_REGEN:
      case EVENT_HOSTILE_ENV:
      case EVENT_MANA_REGEN:
      case EVENT_MOB_HUNT:
      case EVENT_MOB_MUNDANE:
      case EVENT_MOB_SPECIAL:
      case EVENT_MOVE_REGEN:
      case EVENT_MYCONID_EMITTING:
      case EVENT_MYCONID_FX_DECAY:
      case EVENT_NPC_TIMER:
      case EVENT_SHOUT:
      case EVENT_SKILL_RESET:
      case EVENT_SPELLCAST:
      case EVENT_SPELL_MEM:
      case EVENT_SPELL_SCRIBING:
      case EVENT_STUNNED:
      case EVENT_BURN_CHAR:
      case EVENT_MULTI_ROUND_SPELL:
      case EVENT_CHAR_PERIODIC:
      case EVENT_PATH_INIT:
      case EVENT_PATH_DELAY:
      case EVENT_PATH:
         if(e1->actor.a_ch->events == e1)
            { /* head of list */
            e1->actor.a_ch->events = e1->next;
            }
         else
            {
            for(e2 = e1->actor.a_ch->events; e2 && (e2->next != e1); e2 = e2->next)
               ;

            if(!e2)
               {
               /* changed it from a core to wizlog message.. -alth */
               if(e1->actor.a_ch->nr != -1)
                  mvnum = mob_index[e1->actor.a_ch->nr].virtual;
               else
                  mvnum = -1;

               if(e1->actor.a_ch->in_room != NOWHERE)
                  rvnum = world[e1->actor.a_ch->in_room].number;
               else
                  rvnum = -1;

               logit(LOG_MOB, "event for [%d] %s not found in ch->events list in [%d]", mvnum, e1->actor.a_ch->player.name, rvnum);
               dump_stack("missing mob event");
               //        wizlog(51, "event for [%d] %s not found in ch->events list in [%d]", mvnum, e1->actor.a_ch->player.name, rvnum);
               break;  //dump_core();
               }

            e2->next = e1->next;
            }
         break;

      case EVENT_CORPSE_SAVE:
      case EVENT_DECAY:
      case EVENT_FALLING_OBJ:
      case EVENT_OBJ_EXECUTE:
      case EVENT_OBJ_SPECIAL:
      case EVENT_OBJ_TIMER:
         if(e1->actor.a_obj->events == e1)
            { /* head of list */
            e1->actor.a_obj->events = e1->next;
            }
         else
            {
            for(e2 = e1->actor.a_obj->events; e2 && (e2->next != e1); e2 = e2->next)
               ;

            if(!e2)
               {
               logit(LOG_EXIT, "event for %s not found in obj->events list", e1->actor.a_obj->name);
               dump_core();
               }

            e2->next = e1->next;
            }
         break;
      case EVENT_ROOM_EXECUTE:
         if(e1->actor.a_room->events == e1)
            e1->actor.a_room->events = e1->next;
         else
            {
            for(e2 = e1->actor.a_room->events; e2 && (e2->next != e1); e2 = e2->next)
               ;

            if(!e2)
               {
               logit(LOG_EXIT, "event not found in room->events list");
               dump_core();
               }

            e2->next = e1->next;
            }
         break;
      case EVENT_STRINGING:
         /* tricky because it can be either object or character */
         if(e1->target.t_writing->targ_obj)
            {
            if(e1->target.t_writing->targ_obj->events == e1)
               { /* head of list */
               e1->target.t_writing->targ_obj->events = e1->next;
               }
            else
               {
               for(e2 = e1->target.t_writing->targ_obj->events; e2 && (e2->next != e1); e2 = e2->next)
                  ;

               if(!e2)
                  {
                  logit(LOG_EXIT, "event for %s not found in obj->events list", e1->target.t_writing->targ_obj->name);
                  dump_core();
                  }

               e2->next = e1->next;
               }
            }
         else if(e1->target.t_writing->targ_ch)
            {
            if(e1->target.t_writing->targ_ch->events == e1)
               { /* head of list */
               e1->target.t_writing->targ_ch->events = e1->next;
               }
            else
               {
               for(e2 = e1->target.t_writing->targ_ch->events; e2 && (e2->next != e1); e2 = e2->next)
                  ;

               if(!e2)
                  {
                  logit(LOG_EXIT, "event for %s not found in ch->events list", e1->target.t_writing->targ_ch->player.name);
                  dump_core();
                  }

               e2->next = e1->next;
               }
            }
         else
            dump_core();
         break;
      }

   /* move from event_list to avail_events */

   if(event_list == e1)
      {
      event_list = e1->next_event;
      if(event_list)
         event_list->prev_event = NULL;
      }
   else
      {
      e1->prev_event->next_event = e1->next_event;
      if(e1->next_event)
         e1->next_event->prev_event = e1->prev_event;
      }

   if(event_list_tail == e1)
      event_list_tail = e1->prev_event;

   /* avail_events, much simpler, just add to head */

   /* schedule[] list */
   if(schedule[e1->element] == e1)
      {
      schedule[e1->element] = e1->next_sched;
      if(schedule[e1->element])
         schedule[e1->element]->prev_sched = NULL;
      }
   else
      {
      e1->prev_sched->next_sched = e1->next_sched;
      if(e1->next_sched)
         e1->next_sched->prev_sched = e1->prev_sched;
      }

   if(schedule_tail[e1->element] == e1)
      schedule_tail[e1->element] = e1->prev_sched;

   /* event_type_list[] list */
   if(event_type_list[e1->type] == e1)
      {
      event_type_list[e1->type] = e1->next_type;
      if(event_type_list[e1->type])
         event_type_list[e1->type]->prev_type = NULL;
      }
   else
      {
      e1->prev_type->next_type = e1->next_type;
      if(e1->next_type)
         e1->next_type->prev_type = e1->prev_type;
      }

   if(event_type_list_tail[e1->type] == e1)
      event_type_list_tail[e1->type] = e1->prev_type;

   /* update our counters */
   if((event_counter[e1->type] <= 0) || (event_counter[LAST_EVENT] <= 0))
      dump_core();

   event_loading[e1->element]--;
   event_counter[e1->type]--;
   event_counter[LAST_EVENT]--;

   /* clear out the (now) unused pointers */
   e1->prev_type = e1->next_type = e1->prev_sched = e1->next_sched = NULL;
   e1->prev_event = current_event = NULL;
   e1->element = (ubyte) 255;    /* flag to show this struct is unused now */

   mm_release(dead_event_pool, e1);

   return TRUE;
}

/* add an event element to schedule[], returns TRUE on success */
bool Schedule(int type, int pulses, int flag, void *actor, void *target)
{
   P_event e1 = NULL, e2 = NULL;
   int loc;
   int delay;

   if(type >= LAST_EVENT)
      {
      logit(LOG_DEBUG, "Schedule() - Invalid event type %d (LAST_EVENT=%d)", type, LAST_EVENT);
      return FALSE;
      }

   if(!actor)
      {
      logit(LOG_DEBUG, "Schedule() - NULL actor");
      return FALSE;
      }

   if(pulses < 0)
      {
      logit(LOG_DEBUG, "Schedule() - Negative pulses %d", pulses);
      return FALSE;
      }

   delay = pulses;

   /* this next prevents overrun, if an event is added during event processing, it gets added to head of event
      queues, so it doesn't get processed until the next complete cycle (or later, thanks to ReSchedule()), so the
      min delay is 1 while events are being processed. */

   if(current_event && (delay == 0))
      delay++;

   if(delay > MAX_EVENT_TIME)
      delay = MAX_EVENT_TIME;

   if (!dead_event_pool) {
      logit(LOG_EXIT, "Schedule() - dead_event_pool is NULL!");
      return FALSE;
   }
   
   e1 = mm_get(dead_event_pool);
   
   if (!e1) {
      logit(LOG_DEBUG, "Schedule() - mm_get returned NULL!");
      return FALSE;
   }

   e1->prev_event = e1->next_event = NULL;
   e1->prev_sched = e1->next_sched = NULL;
   e1->prev_type = e1->next_type = NULL;
   e1->element = (ubyte) 255;

   /* ok, first we assign actor and target, we do it this way, because if we
      abort out of here, it won't hose up the lists. */

   switch(type)
      { /* one case has to be here for each EVENT_*, though some can be combined */
      case EVENT_NONE:
         e1->actor.a_ch = NULL;
         e1->target.t_ch = NULL;
         break;
      case EVENT_DELAYED_COMMAND:
         e1->actor.a_ch = (P_char) actor;
         if(!target)
            {
            mm_release(dead_event_pool, e1);
            return FALSE;
            }
         e1->target.t_arg = str_dup((char *) target);
         if(!e1->target.t_arg)
            {
            logit(LOG_DEBUG, "Schedule() - str_dup failed for EVENT_DELAYED_COMMAND");
            mm_release(dead_event_pool, e1);
            return FALSE;
            }
         break;
      case EVENT_PATH_DELAY:
         e1->actor.a_ch = (P_char) actor;
         if(!target)
            {
            mm_release(dead_event_pool, e1);
            return FALSE;
            }
         e1->target.t_arg = str_dup((char *) target);
         if(!e1->target.t_arg)
            {
            logit(LOG_DEBUG, "Schedule() - str_dup failed for EVENT_PATH_DELAY");
            mm_release(dead_event_pool, e1);
            return FALSE;
            }
         break;
      case EVENT_BARD_FX_DECAY:
      case EVENT_BARD_SINGING:
      case EVENT_BERSERK:
      case EVENT_COMBAT:
      case EVENT_COMMAND_WAIT:
      case EVENT_MYCONID_FX_DECAY:
      case EVENT_MYCONID_EMITTING:
      case EVENT_SPELL_MEM:
      case EVENT_STUNNED:
         e1->actor.a_ch = (P_char) actor;
         /* Validate the character data before using IS_PC macro */
         if(!e1->actor.a_ch || e1->actor.a_ch->in_room < 0)
            {
            logit(LOG_DEBUG, "Schedule() - Invalid character data for event type %d", type);
            mm_release(dead_event_pool, e1);
            return FALSE;
            }
         if(IS_PC((P_char) actor))
            e1->target.t_ch = NULL;
         else if(type == EVENT_SPELL_MEM)
            e1->target.t_num = (int) target;
         break;
      case EVENT_BALANCE_AFFECTS:
         e1->actor.a_ch = (P_char) actor;
         e1->target.t_ch = NULL;
         /* Validate character before accessing events list */
         if(!e1->actor.a_ch)
            {
            logit(LOG_DEBUG, "Schedule() - NULL character for EVENT_BALANCE_AFFECTS");
            mm_release(dead_event_pool, e1);
            return FALSE;
            }
         /* if one scheduled, don't schedule another */
         if(current_event && (current_event->type == EVENT_BALANCE_AFFECTS) && (current_event->actor.a_ch == e1->actor.a_ch))
            {
            mm_release(dead_event_pool, e1);
            return TRUE;
            }
         for(e2 = e1->actor.a_ch->events; e2; e2 = e2->next)
            {
            if(e2->type == EVENT_BALANCE_AFFECTS)
               {
               mm_release(dead_event_pool, e1);
               return TRUE;
               }
            }
         break;
      case EVENT_SHOUT:   /* Ric */
      case EVENT_AUTO_SAVE:
      case EVENT_PATH:
         e1->actor.a_ch = (P_char) actor;
         e1->target.t_ch = NULL;
         break;
      case EVENT_MOB_HUNT:
         e1->actor.a_ch = (P_char) actor;
         if(!target)
            {
            mm_release(dead_event_pool, e1);
            return FALSE;
            }
         e1->target.t_hunt = (struct hunt_data *) target;
         break;
      case EVENT_AGG_ATTACK:
         e1->actor.a_ch = (P_char) actor;
         if(!target)
            {
            mm_release(dead_event_pool, e1);
            return FALSE;
            }
         e1->target.t_ch = (P_char) target;
         break;
      case EVENT_CHAR_EXECUTE:
      case EVENT_BARD_EXECUTE:
      case EVENT_HOSTILE_ENV:
      case EVENT_BURN_CHAR:
      case EVENT_MULTI_ROUND_SPELL:
      case EVENT_DISGUISE:
         e1->actor.a_ch = (P_char) actor;
         e1->target.t_func = target;
         break;
#ifdef PCPROCS
      case EVENT_CHAR_PERIODIC:
         e1->actor.a_ch = (P_char) actor;
         e1->target.t_func_proc = target;
         break;
#endif
#ifdef EVENT_SAVING
      case EVENT_JUSTICE_DEBT:
      case EVENT_SCHAR_EXECUTE:
         e1->actor.a_ch = (P_char) actor;
         e1->target.t_sced = target;
         break;
#endif
      case EVENT_ROOM_EXECUTE:
         CREATE(e1->target.t_r_e_d, struct room_exec_data, 1);
         e1->target.t_r_e_d->ch = (P_char) actor;
         e1->target.t_r_e_d->re_func = target;
         if(e1->target.t_r_e_d->ch && e1->target.t_r_e_d->ch->in_room >= 0 && e1->target.t_r_e_d->ch->in_room < top_of_world)
            e1->actor.a_room = &world[e1->target.t_r_e_d->ch->in_room];
         else
            {
            free(e1->target.t_r_e_d);
            e1->target.t_r_e_d = NULL;
            mm_release(dead_event_pool, e1);
            return FALSE;
            }
         break;
      case EVENT_CORPSE_SAVE:
         e1->actor.a_obj = (P_obj) actor;
         /* if one scheduled, don't schedule another */
         if(current_event && (current_event->type == EVENT_CORPSE_SAVE) && (current_event->actor.a_obj == e1->actor.a_obj))
            {
            mm_release(dead_event_pool, e1);
            return TRUE;
            }
         for(e2 = e1->actor.a_obj->events; e2; e2 = e2->next)
            {
            if(e2->type == EVENT_CORPSE_SAVE)
               {
               mm_release(dead_event_pool, e1);
               return TRUE;
               }
            }
         break;
      case EVENT_OBJ_EXECUTE:
         e1->actor.a_obj = (P_obj) actor;
         e1->target.t_func_o = target;
         break;
      case EVENT_SPELL_SCRIBING:
         e1->actor.a_ch = (P_char) actor;
         e1->target.t_scribe = (struct scribing_data_type *) target;
         break;
      case EVENT_MOB_MUNDANE:
      case EVENT_MOB_SPECIAL:
      case EVENT_NPC_TIMER:
      case EVENT_PATH_INIT:
         e1->actor.a_ch = (P_char) actor;
         if(target)
            {
            e1->target.t_arg = str_dup((char *) target);
            if(!e1->target.t_arg)
               {
               logit(LOG_DEBUG, "Schedule() - str_dup failed for event type %d", type);
               mm_release(dead_event_pool, e1);
               return FALSE;
               }
            }
         else
            e1->target.t_arg = NULL;
         break;

      case EVENT_SPELLCAST:
         e1->actor.a_ch = (P_char) actor;
         if(target)
            e1->target.t_spell = (struct spellcast_datatype *) target;
         else
            e1->target.t_spell = NULL;

         if(pulses > 4)
            dump_core();
         break;

      case EVENT_FALLING_CHAR:
      case EVENT_HIT_REGEN:
      case EVENT_MOVE_REGEN:
      case EVENT_MANA_REGEN:
         e1->actor.a_ch = (P_char) actor;
         e1->target.t_num = (int) target;
         break;

      case EVENT_SKILL_RESET:
         e1->actor.a_ch = (P_char) actor;
         if(target)
            e1->target.t_num = (int) target;
         else
            dump_core();
         break;

      case EVENT_OBJ_SPECIAL:
      case EVENT_OBJ_TIMER:
         e1->actor.a_obj = (P_obj) actor;
         if(target)
            {
            e1->target.t_arg = str_dup((char *) target);
            if(!e1->target.t_arg)
               {
               logit(LOG_DEBUG, "Schedule() - str_dup failed for event type %d", type);
               mm_release(dead_event_pool, e1);
               return FALSE;
               }
            }
         else
            e1->target.t_arg = NULL;
         break;

      case EVENT_FALLING_OBJ:
         e1->actor.a_obj = (P_obj) actor;
         e1->target.t_num = (int) target;
         break;

      case EVENT_DECAY:
         e1->actor.a_obj = (P_obj) actor;
         e1->target.t_ch = NULL;
         break;

      case EVENT_ROOM_TIMER:
      case EVENT_ROOM_SPECIAL:
         e1->actor.a_room = (P_room) actor;
         if(target)
            {
            e1->target.t_arg = str_dup((char *) target);
            if(!e1->target.t_arg)
               {
               logit(LOG_DEBUG, "Schedule() - str_dup failed for event type %d", type);
               mm_release(dead_event_pool, e1);
               return FALSE;
               }
            }
         else
            e1->target.t_arg = NULL;
         break;

      case EVENT_RESET_ZONE:
         e1->actor.a_room = NULL;
         e1->target.t_num = ((int) actor) - 1;
         break;

      case EVENT_SPECIAL:
         e1->actor.a_func = (void *) actor;
         if(target)
            {
            e1->target.t_arg = str_dup((char *) target);
            if(!e1->target.t_arg)
               {
               logit(LOG_DEBUG, "Schedule() - str_dup failed for EVENT_SPECIAL");
               mm_release(dead_event_pool, e1);
               return FALSE;
               }
            }
         else
            e1->target.t_arg = NULL;
         break;

      case EVENT_VOID_FN_EXECUTE:
         e1->actor.a_func_param = (void *) actor;
         if(target)
            e1->target.t_param = target;
         else
            e1->target.t_arg = NULL;
         break;

      case EVENT_BRAIN_DRAINING:
         e1->actor.a_ch = (P_char) actor;
         if(!target)
            {
            mm_release(dead_event_pool, e1);
            return FALSE;
            }
         e1->target.t_ch = (P_char) target;
         break;

      case EVENT_STRINGING:
         /* we are a weirdie, check target first, because we have to use the info in there to determine where to attach
          * this event. */
         if(!target)
            {
            mm_release(dead_event_pool, e1);
            return FALSE;
            }
         e1->target.t_writing = (struct writing_info *) target;
         if(e1->target.t_writing->targ_ch)
            {
            e1->actor.a_ch = (P_char) e1->target.t_writing->targ_ch;
            for(e2 = e1->actor.a_ch->events; e2; e2 = e2->next)
               {
               if(e2->type == EVENT_STRINGING)
                  {
                  mm_release(dead_event_pool, e1);
                  return TRUE;
                  }
               }
            }
         else if(e1->target.t_writing->targ_obj)
            {
            e1->actor.a_obj = (P_obj) e1->target.t_writing->targ_obj;
            for(e2 = e1->actor.a_obj->events; e2; e2 = e2->next)
               {
               if(e2->type == EVENT_STRINGING)
                  {
                  mm_release(dead_event_pool, e1);
                  return TRUE;
                  }
               }
            }
         else
            {
            mm_release(dead_event_pool, e1);
            return FALSE;
            }

         e1->target.t_writing->event = e1;
         break;

         //      case EVENT_SHIP_MOVE:
      default:
         mm_release(dead_event_pool, e1);
         return FALSE;
         break;
      }

   /* ok, valid data, now make sure all the pointers are pointing correctly */
   /* find right pulse to add this event */

   /* Validate global pulse variable */
   if(pulse < 0 || pulse >= 240)
      {
      logit(LOG_EXIT, "Schedule() - Invalid global pulse value: %d", pulse);
      mm_release(dead_event_pool, e1);
      return FALSE;
      }

   loc = delay % 240;
   loc += pulse;
   if(loc > 239)
      loc -= 240;

   /* Additional defensive check for loc bounds */
   if(loc < 0 || loc >= 240)
      {
      logit(LOG_EXIT, "Schedule() - loc calculation resulted in invalid value: %d (delay=%d, pulse=%d)", loc, delay, pulse);
      mm_release(dead_event_pool, e1);
      return FALSE;
      }

   /* make sure everything is initialized */

   e1->type = type;
   e1->one_shot = flag;
   e1->timer = (delay / 240) + 1;
   e1->element = loc;
   
   /* Validate timer didn't overflow */
   if(e1->timer <= 0)
      {
      logit(LOG_DEBUG, "Schedule() - Invalid timer calculation: %d (delay=%d)", e1->timer, delay);
      mm_release(dead_event_pool, e1);
      return FALSE;
      }

   /* hehe, nothing like maintaining 5 independent lists!  Gets complicated, but it's lightning fast now */
   switch(e1->type)
      {
      case EVENT_AGG_ATTACK:
      case EVENT_AUTO_SAVE:
      case EVENT_BARD_FX_DECAY:
      case EVENT_BARD_SINGING:
      case EVENT_BERSERK:
      case EVENT_COMBAT:
      case EVENT_COMMAND_WAIT:
      case EVENT_DELAYED_COMMAND:
      case EVENT_PATH_DELAY:
      case EVENT_DISGUISE:
      case EVENT_FALLING_CHAR:
      case EVENT_HIT_REGEN:
      case EVENT_MANA_REGEN:
      case EVENT_MOB_HUNT:
      case EVENT_MOB_MUNDANE:
      case EVENT_MOB_SPECIAL:
      case EVENT_MOVE_REGEN:
      case EVENT_MYCONID_EMITTING:
      case EVENT_MYCONID_FX_DECAY:
      case EVENT_NPC_TIMER:
      case EVENT_SHOUT:   /* Ric */
      case EVENT_SKILL_RESET:
      case EVENT_PATH_INIT:
      case EVENT_PATH:
      case EVENT_SPELLCAST:
      case EVENT_SPELL_MEM:
      case EVENT_SPELL_SCRIBING:
      case EVENT_STUNNED:
      case EVENT_BURN_CHAR:
      case EVENT_MULTI_ROUND_SPELL:
      case EVENT_CHAR_PERIODIC:
      case EVENT_BALANCE_AFFECTS:
      case EVENT_BRAIN_DRAINING:
      case EVENT_CHAR_EXECUTE:
      case EVENT_BARD_EXECUTE:
      case EVENT_HOSTILE_ENV:
#ifdef EVENT_SAVING
      case EVENT_JUSTICE_DEBT:
      case EVENT_SCHAR_EXECUTE:
#endif
         e1->next = e1->actor.a_ch->events;
         e1->actor.a_ch->events = e1;
         break;
      case EVENT_STRINGING:
         if(e1->target.t_writing->targ_ch)
            {
            e1->next = e1->target.t_writing->targ_ch->events;
            e1->target.t_writing->targ_ch->events = e1;
            }
         else
            {
            e1->next = e1->target.t_writing->targ_obj->events;
            e1->target.t_writing->targ_obj->events = e1;
            }
         break;
      case EVENT_CORPSE_SAVE:
      case EVENT_DECAY:
      case EVENT_FALLING_OBJ:
      case EVENT_OBJ_SPECIAL:
      case EVENT_OBJ_TIMER:
      case EVENT_OBJ_EXECUTE:
         e1->next = e1->actor.a_obj->events;
         e1->actor.a_obj->events = e1;
         break;
      case EVENT_ROOM_EXECUTE:
         e1->next = e1->actor.a_room->events;
         e1->actor.a_room->events = e1;
         break;
      }

   if(!schedule[loc])
      {
      schedule[loc] = e1;
      e1->prev_sched = NULL;
      }
   else
      {
      schedule_tail[loc]->next_sched = e1;
      e1->prev_sched = schedule_tail[loc];
      }

   schedule_tail[loc] = e1;
   event_loading[loc]++;

   if(!event_type_list[type])
      {
      event_type_list[type] = e1;
      e1->prev_type = NULL;
      }
   else
      {
      event_type_list_tail[type]->next_type = e1;
      e1->prev_type = event_type_list_tail[type];
      }

   event_type_list_tail[type] = e1;
   event_counter[type]++;

   if(!event_list)
      {
      event_list = e1;
      e1->prev_event = NULL;
      }
   else
      {
      event_list_tail->next_event = e1;
      e1->prev_event = event_list_tail;
      }

   event_list_tail = e1;
   e1->next_event = NULL;
   event_counter[LAST_EVENT]++;

   if((event_loading[loc] > 1) && (event_loading[loc] > event_counter[LAST_EVENT] / 24))
      ReSchedule();

   return TRUE;
}

/* called LAST in boot_world(), so that we have access to all of the mobs/objs/
   specials/etc.  Set things up and schedule the first set of events. */

void init_events(void)
{
   ubyte i;
   int j;
   char Gbuf1[MAX_STRING_LENGTH];

   /* make sure all the arrays are initialized */

   for(i = 0; i < LAST_EVENT; i++)
      {
      event_counter[i] = 0;
      event_type_list[i] = NULL;
      event_type_list_tail[i] = NULL;
      }

   event_counter[LAST_EVENT] = 0;

   for(i = 0; i < 240; i++)
      {
      schedule[i] = NULL;
      schedule_tail[i] = NULL;
      event_loading[i] = 0;
      }

   /* create the initial set of event elements */
   j = top_of_zone_table * 2 + top_of_mobt * 3;  /* approximate */

   avail_events = NULL;

   dead_event_pool = mm_create("EVENTS", sizeof(struct event_data), offsetof(struct event_data, next), 11);

   logit(LOG_STATUS, "%d initial event elements allocated.\n", event_counter[LAST_EVENT + 1]);

   logit(LOG_STATUS, "assigning room specials events.");

   // Added additional sanity check to allow for null (empty) rooms. --MIAX 10/17/00
   for(j = 0; j < top_of_world; j++)
      {
      /* Sanity check for world array bounds */
      if (j < 0 || j >= top_of_world) {
         logit(LOG_DEBUG, "init_events() - Invalid room index %d (top_of_world=%d)", j, top_of_world);
         continue;
      }
      
      if(!IS_CSET(world[j].room_flags, RESERVED_OLC))
         {
         if(world[j].funct)
            {
            /* Try to call the room special with PROC_INITIALIZE */
            int result = 0;
            
            /* Wrap in a check to catch potential crashes */
            if (world[j].funct) {
               result = (*world[j].funct) (j, 0, PROC_INITIALIZE, 0);
            }
            
            if(result)
               {
               AddEvent(EVENT_ROOM_SPECIAL, PULSE_MOBILE + number(-4, 4), TRUE, &world[j], 0);
               }
            }
         }
      }

   logit(LOG_STATUS, "%d room special events assigned.", event_counter[EVENT_ROOM_SPECIAL]);

   /* do boot_time reset of zones and fire up their reset events, it staggers
      them over the entire pulse, because zone resets are heavy-duty CPU hogs
      so we don't want them all at once. */
   
   if(top_of_zone_table != 0)
      i = MAX(1, 240 / top_of_zone_table);
   else
      i = 1;

   logit(LOG_STATUS, "boot time reset of all zones.");
   
   for(pulse = 0, j = 0; j <= top_of_zone_table; pulse += i, j++)
      {
      if(pulse > 239)
         pulse -= 240;

      /* Sanity check for zone_table bounds */
      if (j < 0 || j > top_of_zone_table) {
         logit(LOG_DEBUG, "init_events() - Invalid zone index %d (top_of_zone_table=%d)", j, top_of_zone_table);
         break;
      }
      
      /* Check if zone_table[j] is valid */
      if (!zone_table) {
         logit(LOG_EXIT, "init_events() - zone_table is NULL!");
         break;
      }
      
      logit(LOG_STATUS, "zone %3d:(%5d-%5d) %s",
            j, j ? (zone_table[j - 1].top + 1) : 0, zone_table[j].top, zone_table[j].name);

      if(zone_table[j].reset_mode)
         {
         AddEvent(EVENT_RESET_ZONE, zone_table[j].lifespan * 240, FALSE, j + 1, 0);
         }

      /* zone weather */
      if(j < top_of_zone_table)
         {
         /* no weather for 'End of World' zone */
         sprintf(Gbuf1, "%d", j);
         AddEvent(EVENT_SPECIAL, 240 + number(-9, 9), TRUE, weather_change, Gbuf1);
         }

      current_event = 0;
      zone_table[j].time_last_boot = time(0);
      reset_zone(j);
      }

   pulse = 0;

   /* special cases now */

   /* Zone Noise Specials */
   AddEvent(EVENT_SPECIAL, PULSE_MOBILE, TRUE, waterdeep_city_noises, 0);
   AddEvent(EVENT_SPECIAL, PULSE_MOBILE, TRUE, undermountainDungeonNoises, 0);
   AddEvent(EVENT_SPECIAL, PULSE_MOBILE, TRUE, gloomhaven_city_noises, 0);

   /* game clock */
   AddEvent(EVENT_SPECIAL, 240 - pulse, FALSE, another_hour, 0);

   /* sunrise, sunset, etc informer */
   AddEvent(EVENT_SPECIAL, 500, TRUE, astral_clock, NULL);
#ifdef NEWJUSTICE
   /* justice main engine */
   if(IS_ENABLED(CODE_NEWJUSTICE))
      AddEvent(EVENT_SPECIAL, 150, TRUE, justice_engine1, NULL);
#endif

   if(port == DFLT_MAIN_PORT)
      { /* Mud Stats Webfile - 5 minute intervals */
      AddEvent(EVENT_SPECIAL, 1200, TRUE, mud_info_web, NULL);
      }

   /* Mud Stat Logfile - 1 hour interval */
   AddEvent(EVENT_SPECIAL, 14400, TRUE, mud_info_log, NULL);

   /* write slot machine jackpots hourly */
   AddEvent(EVENT_SPECIAL, 14400, TRUE, write_jackpot, NULL);

   /* Bloodstone city gate */
   AddEvent(EVENT_SPECIAL, 240 - pulse, FALSE, bs_gate, 0);
   /* rather than add a new function, set initial room light values here */

   /* Disc space check */
   AddEvent(EVENT_SPECIAL, 240, TRUE, checkDiscSpace, 0);

   /* timed house control stuff */
#ifdef KINGDOM
   AddEvent(EVENT_SPECIAL, 500 - pulse, FALSE, do_housekeeping, 0);
#endif

   // Added additional sanity check to allow for null (empty) rooms. --MIAX 10/17/00
   for(j = 0; j < top_of_world; j++)
      {
      if(!IS_CSET(world[j].room_flags, RESERVED_OLC))
         {
         room_light(j, REAL);
         }
      }

   logit(LOG_STATUS, "%d events scheduled.", event_counter[LAST_EVENT]);
}

/* heart and soul of event driver system.  Called from game_loop only, at most,
   once per pulse.  Decrements the timers and triggers the events that should
   happen NOW.  No args, because everything it deals with is global scope. */

void Events(void)
{
   P_event e1, c_e;
   P_char t_ch;
   P_obj t_obj;
   P_room t_room;
   char *t_arg;
   int i, j;
   struct func_attachment *fn;
   struct func_registration_data *frd;
   struct affected_type *af_order, *next_af;
   char standup[10];

   if((pulse < 0) || (pulse > 239))
      {
      logit(LOG_EXIT, "pulse (%d) out of range in Events", pulse);
      dump_core();
      }

   // yank
   if(pulse > 0)
      {
      for(c_e = schedule[pulse - 1]; c_e; c_e = e1)
         {
         e1 = c_e->next_sched;
         if(c_e->type == EVENT_SPELLCAST)
            {
            debuglog(51, DS_COMBAT, "%d Events(): WARNING .. detected an EVENT_SPELLCAST in previous pulse.", pulse );
            debuglog(51, DS_COMBAT, "%d Events(): [%s] timer %d", pulse, GET_NAME(c_e->actor.a_ch), c_e->timer);
            }
         }
      }
   // yank

   for(c_e = schedule[pulse]; c_e; c_e = e1)
      {
      current_event = c_e;
      e1 = c_e->next_sched;

      if(c_e->type == EVENT_RESET_ZONE)  /* age the zones */
         zone_table[c_e->target.t_num].age++;

      if(--(c_e->timer) > 0)
         continue;

      /* ok, trigger time */
      switch(c_e->type)
         { /* one case has to be here for each EVENT_* */
         case EVENT_NONE:
            break;

         case EVENT_DELAYED_COMMAND:
            t_ch = (P_char) c_e->actor.a_ch;
            if(t_ch && (t_arg = (char *) c_e->target.t_arg))
               command_interpreter(t_ch, t_arg);
            break;

         case EVENT_PATH_DELAY:
            t_ch = (P_char) c_e->actor.a_ch;
            if(t_ch && (t_arg = (char *) c_e->target.t_arg))
               command_interpreter(t_ch, t_arg);
            break;

         case EVENT_COMMAND_WAIT:
            t_ch = (P_char) c_e->actor.a_ch;
            if(t_ch)
               {
               t_ch->specials.command_delays = FALSE;
               if(t_ch->in_room != NOWHERE)
                  {
                  update_pos(t_ch);
                  if(IS_PET(t_ch) && IS_AFFECTED(t_ch, AFF_ORDERED) &&
                     t_ch->following && IS_AFFECTED(t_ch, AFF_CHARM))
                     {
                     for(af_order = t_ch->affected; af_order; af_order = next_af)
                        {
                        next_af = af_order->next;
                        if(af_order->type == TYPE_ORDERED)
                           break;
                        }

                     if(af_order)
                        {
                        if(CAN_ACT(t_ch))
                           {
                           if(GET_POS(t_ch) != POS_STANDING)
                              {
                              sprintf(standup, "stand");
                              command_interpreter(t_ch, language_crypt(t_ch->following, t_ch, standup));
                              CharWait(t_ch, PULSE_VIOLENCE / 4);
                              }
                           else
                              {
                              command_interpreter(t_ch, language_crypt(t_ch->following, t_ch, af_order->instruction));
                              affect_remove(t_ch, af_order);
                              }
                           }
                        }
                     }
                  }
               }
            break;

         case EVENT_HIT_REGEN:
            t_ch = (P_char) c_e->actor.a_ch;
            if(!t_ch)
               break;

            j = (int) c_e->target.t_num;
            if((GET_HIT(t_ch) < GET_MAX_HIT(t_ch)) || (j < 1))
               GET_HIT(t_ch) += j;

            i = hit_regen(t_ch);
            if(would_die(t_ch, 0))
               {
               SuddenDeath(t_ch, t_ch, "bled to death");
               t_ch = NULL;
               break;
               }

            update_pos(t_ch);
            if(((GET_HIT(t_ch) == GET_MAX_HIT(t_ch)) && (i > 0) && (j == 0)))
               break;
            if(t_ch)
               StartRegen(t_ch, EVENT_HIT_REGEN);
            break;

         case EVENT_MOVE_REGEN:
            t_ch = (P_char) c_e->actor.a_ch;
            if(!t_ch)
               break;

            j = (int) c_e->target.t_num;
            GET_MOVE(t_ch) += j;
            i = move_regen(t_ch);

            if(((GET_MOVE(t_ch) == GET_MAX_MOVE(t_ch)) && (i > 0) && (j == 0)) || ((GET_MOVE(t_ch) < 0) && (i < 0)))
               break;

            StartRegen(t_ch, EVENT_MOVE_REGEN);
            current_event = c_e;
            break;

         case EVENT_PATH:
            t_ch = (P_char) c_e->actor.a_ch;
            if(!t_ch)
               break;

            path_handler(t_ch, NULL, PROC_EVENT, NULL);
            break;

         case EVENT_PATH_INIT:
            t_ch = (P_char) c_e->actor.a_ch;
            if(!t_ch)
               break;

            if(c_e->target.t_arg)
               t_arg = (char *) c_e->target.t_arg;
            else
               t_arg = NULL;

            socials_init_path(t_ch, t_arg);
            break;

         case EVENT_MANA_REGEN:
            t_ch = (P_char) c_e->actor.a_ch;
            if(!t_ch)
               break;

            j = (int) c_e->target.t_num;
            GET_MANA(t_ch) += j;
            i = mana_regen(t_ch);
            if(((GET_MANA(t_ch) == GET_MAX_MANA(t_ch)) && (i > 0) && (j == 0)) || ((GET_MANA(t_ch) < 0) && (i < 0)))
               break;

            StartRegen(t_ch, EVENT_MANA_REGEN);
            break;

         case EVENT_COMBAT:
         case EVENT_SHOUT: /* Ric */
            break;

         case EVENT_BALANCE_AFFECTS:
            t_ch = (P_char) c_e->actor.a_ch;
            if(t_ch)
               affect_total(t_ch);
            break;

#ifndef NEW_BARD
         case EVENT_BARD_SINGING:
            t_ch = (P_char) c_e->actor.a_ch;
            if(t_ch)
               BardSing(t_ch);
            break;

         case EVENT_BARD_FX_DECAY:
            t_ch = (P_char) c_e->actor.a_ch;
            if(t_ch)
               BardEffect(t_ch);
            break;
#endif

#if 0
         case EVENT_MYCONID_EMITTING:
            t_ch = (P_char) c_e->actor.a_ch;
            if(t_ch)
               MyconidEmit(t_ch);
            break;

         case EVENT_MYCONID_FX_DECAY:
            t_ch = (P_char) c_e->actor.a_ch;
            if(t_ch)
               MyconidEffect(t_ch);
            break;
#endif

         case EVENT_CHAR_EXECUTE:
         case EVENT_BARD_EXECUTE:
         case EVENT_HOSTILE_ENV:
            t_ch = (P_char) c_e->actor.a_ch;
            if(c_e->target.t_func && t_ch)
               ((*c_e->target.t_func) (t_ch));
            //      if ((*c_e->target.t_func) == release_mob_mem)        // UNCOMMENT after testing -- alth
            //      c_e = NULL;
            break;

#ifdef EVENT_SAVING
         case EVENT_JUSTICE_DEBT:
         case EVENT_SCHAR_EXECUTE:
            t_ch = (P_char) c_e->actor.a_ch;
            if(c_e->target.t_sced->t_func && t_ch)
               ((*c_e->target.t_sced->t_func) (t_ch, c_e->target.t_sced->t_arg, c_e->target.t_sced->t_arg_size));
            break;
#endif

         case EVENT_ROOM_EXECUTE:
            t_room = (P_room) c_e->actor.a_room;
            t_ch = (P_char) c_e->target.t_r_e_d->ch;
            if(c_e->target.t_r_e_d->re_func && t_ch && t_room)
               ((*c_e->target.t_r_e_d->re_func) (t_ch, t_room));
            break;

         case EVENT_CORPSE_SAVE:
            t_obj = (P_obj) c_e->actor.a_obj;
            if(t_obj)
               writeCorpse(t_obj);
            break;

         case EVENT_OBJ_EXECUTE:
            t_obj = (P_obj) c_e->actor.a_obj;
            if(c_e->target.t_func_o && t_obj)
               ((*c_e->target.t_func_o) (t_obj));
            break;

         case EVENT_SPELLCAST:
            t_ch = (P_char) c_e->actor.a_ch;
            if(t_ch)
               SpellCastProcess(t_ch, c_e->target.t_spell);
            break;

         case EVENT_CHAR_PERIODIC:
            t_ch = (P_char) c_e->actor.a_ch;
            (*c_e->target.t_func_proc) (t_ch, NULL, PROC_EVENT, NULL);
            frd = findProcFunctionByPtr(c_e->target.t_func);
            if(frd)
               {
               /* gotta find the proc fn on the ch in order to see if it wasnt
                * removed by the proc we just called.. If found we check if its
                * _still_ IDX_PERIODIC (in case the proc wants it off for some reason)
                * and reschedule it if still on                             -- Alth  */
               fn = findAttachedChProc(t_ch, frd->name);
               if(fn && IS_SET(fn->proc_flag, IDX_PERIODIC))
                  AddEvent(EVENT_CHAR_PERIODIC, frd->repeatTime, TRUE, t_ch, frd->ptr);
               }
            break;

         case EVENT_NPC_TIMER:
            /* This has nothing to do with periodic events...so we just call...*/
            t_ch = (P_char) c_e->actor.a_ch;
            if(c_e->target.t_arg)
               t_arg = (char *) c_e->target.t_arg;
            else
               t_arg = NULL;

            if(t_ch && GET_SPEC_FN(t_ch))
               {
               fn = GET_SPEC_FN(t_ch);
               while(fn)
                  {
                  if((*fn->func.ch) (t_ch, 0, PROC_EVENT, t_arg))
                     break;
                  fn = fn->next;
                  }
               }
            break;

         case EVENT_MOB_SPECIAL:
            t_ch = (P_char) c_e->actor.a_ch;
            if(t_ch && NewMobSpecials(t_ch) && (c_e->element != (ubyte) 255) && (t_ch == (P_char) c_e->actor.a_ch))
               AddEvent(EVENT_MOB_SPECIAL, PULSE_MOBILE + number(-4, 4), TRUE, t_ch, 0);
            break;

         case EVENT_MOB_MUNDANE:
            t_ch = (P_char) c_e->actor.a_ch;
            if(t_ch && NewMobAct(t_ch) && (c_e->element != (ubyte) 255) && (t_ch == (P_char) c_e->actor.a_ch))
               AddEvent(c_e->type, PULSE_MOBILE + number(-4, 4), TRUE, t_ch, 0);
            break;

         case EVENT_FALLING_CHAR:
            t_ch = (P_char) c_e->actor.a_ch;
            if(t_ch)
               falling_char(t_ch);
            break;

         case EVENT_BERSERK:
            t_ch = (P_char) c_e->actor.a_ch;
            if(t_ch)
               Berserk(t_ch, FALSE, SKILL_BERSERK);
            break;

         case EVENT_DISGUISE:
            t_ch = (P_char) c_e->actor.a_ch;
            if(t_ch && IS_PC(t_ch) && (GET_CLASS(t_ch) == CLASS_ILLUSIONIST))
               disguiseEvent(t_ch, 2);
            break;

         case EVENT_DECAY:
            t_obj = (P_obj) c_e->actor.a_obj;
            if(t_obj)
               {
               Decay(t_obj);
               t_obj = NULL;
               c_e->actor.a_obj = NULL;
               }
            break;

         case EVENT_OBJ_SPECIAL:
         case EVENT_OBJ_TIMER:
            t_obj = (P_obj) c_e->actor.a_obj;
            if(c_e->target.t_arg)
               t_arg = (char *) c_e->target.t_arg;
            else
               t_arg = NULL;

            if(t_obj && t_obj->R_num >= 0 && t_obj->R_num < top_of_objt && obj_index[t_obj->R_num].func)
               {
               fn = obj_index[t_obj->R_num].func;
               while(fn)
                  {
                  if((*fn->func.obj) (t_obj, 0, PROC_EVENT, t_arg))
                     break;
                  fn = fn->next;
                  }
               }

            if((c_e->type != EVENT_OBJ_TIMER) && t_obj && (c_e->element != (ubyte) 255) && c_e->one_shot)
               AddEvent(c_e->type, PULSE_MOBILE + number(-4, 4), TRUE, t_obj, 0);
            break;

         case EVENT_ROOM_TIMER:
         case EVENT_ROOM_SPECIAL:
            t_room = (P_room) c_e->actor.a_room;
            if(c_e->target.t_arg)
               t_arg = (char *) c_e->target.t_arg;
            else
               t_arg = NULL;

            if(t_room && t_room->funct && *t_room->funct)
               {
               (*t_room->funct) (real_room(t_room->number), 0, PROC_EVENT, t_arg);
               if(c_e->type != EVENT_ROOM_TIMER)
                  AddEvent(c_e->type, PULSE_MOBILE + number(-4, 4), TRUE, t_room, t_arg);
               }
            break;

         case EVENT_RESET_ZONE:
            reset_zone((int) c_e->target.t_num);
            break;

         case EVENT_SPECIAL:
            (c_e->actor.a_func) ();
            break;

         case EVENT_VOID_FN_EXECUTE:
            (c_e->actor.a_func_param) (c_e->target.t_param);
            break;

         case EVENT_AUTO_SAVE:
            t_ch = (P_char) c_e->actor.a_ch;
            if(t_ch && (t_ch->in_room != NOWHERE))
               do_save_silent(t_ch, 1);
            break;

         case EVENT_SPELL_SCRIBING:
            if(c_e->actor.a_ch)
               handle_scribe(c_e->actor.a_ch, c_e->target.t_scribe);
            break;

         case EVENT_SPELL_MEM:
            if(c_e->actor.a_ch)
               {
               if(IS_NPC(c_e->actor.a_ch))
                  restore_npc_spell(c_e->actor.a_ch);
               else
                  handle_spell_mem(c_e->actor.a_ch);
               }
            break;

         case EVENT_STUNNED:
            t_ch = (P_char) c_e->actor.a_ch;
            if(t_ch)
               {
               REMOVE_CBIT(t_ch->specials.affects, AFF_STUNNED);
               send_to_char("The world stops spinning.\n", t_ch);
               act("$n's senses seem to clear again!", FALSE, t_ch, 0, 0, TO_ROOM);
               }
            break;

         case EVENT_AGG_ATTACK:
            AggAttack();
            break;

         case EVENT_FALLING_OBJ:
            t_obj = (P_obj) c_e->actor.a_obj;
            if(t_obj)
               falling_obj(t_obj);
            break;

         case EVENT_SHIP_MOVE:
         case EVENT_SKILL_RESET:
            /* unsure how these will be handled just yet, so, do nothing. JAB */
            break;

         case EVENT_MOB_HUNT:
            if(c_e->actor.a_ch && c_e->target.t_hunt)
               if(NewMobHunt())
                  {
                  /* okay.. I need to rescedule this event with the same t_hunt.  However, I don't want to allocate
                     a new struct.. so I'll play some games and manipulate code a bit <cackle> */

                  /* first re-add the event... */
                  if(IS_AFFECTED(c_e->actor.a_ch, AFF_PATH))
                     {
                     // debuglog(51, DS_SHEVARASH, "AFF_PATH hunting: %d!!!!", c_e->target.t_hunt->hunt_type);
                     }

                  AddEvent(EVENT_MOB_HUNT, PULSE_MOB_HUNT, TRUE, c_e->actor.a_ch, c_e->target.t_hunt);

                  /* then make it look like the old event had no data... */
                  c_e->target.t_hunt = NULL;
                  }
            break;

         case EVENT_BRAIN_DRAINING:
            if(c_e->actor.a_ch && c_e->target.t_ch)
               illithid_feeding(c_e->actor.a_ch, c_e->target.t_ch);
            break;

         case EVENT_STRINGING:
            /* should never, ever happen, but just in case someone manages to continue writing for more than 45 days
             * straight... */
            c_e->timer = 64799;
            c_e->one_shot = FALSE;
            break;

            /* Similar to EVENT_CHAR_EXECUTE, but only for specific burning events,  */
            /* for use in new spells/procs.                                          */
         case EVENT_BURN_CHAR:
            t_ch = (P_char) c_e->actor.a_ch;
            if(t_ch && c_e->target.t_func)
               ((*c_e->target.t_func) (t_ch));

            break;

         case EVENT_MULTI_ROUND_SPELL:
            t_ch = (P_char) c_e->actor.a_ch;
            if(t_ch && c_e->target.t_func)
               ((*c_e->target.t_func) (t_ch));
            break;

         default:
            logit(LOG_EXIT, "Bogus event type (%d) in Events.", c_e->type);
            dump_core();
            break;
         }

      /* Fix to the infamous spellcast bug thats been plaguing us for the past 5 years..
       * Actually what happened was a combination of some event (c_e) followed by an
       * EVENT_COMMAND_WAIT, if during the exec of c_e (above) the following EVENT_COMMAND_WAIT
       * got rescheduled to a later pulse, e1->next_sched was NULL thus aborting the loop
       * without precessing the remaining events.. ie it wasnt only spellcast which was getting
       * fucked, but all kinds of events were delayed for atleast a minute  -- Altherog Jun 1999 */
      if(e1 && !e1->next_sched && e1->element == (ubyte) 255 && c_e->next_sched)
         e1 = c_e->next_sched;

      if(c_e && c_e->one_shot && (c_e->element != (ubyte) 255))
         {
         current_event = c_e;
         RemoveEvent();
         }
      }
}

void showEventLoad(P_char ch, char *arg)
{
   int i;
   char buf[MAX_STRING_LENGTH];

   buf[0] = 0;
   for(i = 0; i < 240; i += 4)
      {
      sprintf(buf + strlen(buf), "[%-3d]  %-5d  - [%-3d]  %-5d  - [%-3d]  %-5d -  [%-3d]  %-5d\n",
              i, event_loading[i],
              i+1, event_loading[i+1],
              i+2, event_loading[i+2],
              i+3, event_loading[i+3]);
      }

   send_to_char(buf, ch);

}


# Outcast MUD Trade System Analysis

## System Overview

The Outcast MUD trade system implements a sophisticated economy based on 46 commodities across different town types. Each trade center has production and consumption rates that scale with population and town specialization.

## Commodity System

### All 46 Commodities Identified

**Precious Metals & Coins (0-3):**
- Gold (10,000 copper value)
- Silver (1,000 copper value) 
- Copper (1,000 copper value)
- Platinum (100,000 copper value)

**Base Metals (4-10):**
- Iron (100 copper value)
- Bronze (1,000 copper value)
- Adamantium (5,000 copper value)
- Mithril (15,000 copper value)
- Steel (200 copper value)
- Lead (100 copper value)
- Tin (100 copper value)

**Gems (11-13):**
- Diamond (100,000 copper value)
- Emerald (50,000 copper value)
- Ruby (40,000 copper value)

**Food & Consumables (14-28):**
- Meat (1,000 copper value)
- Salt (2,000 copper value)
- Spices (5,000 copper value)
- Milk (400 copper value)
- Ale (1,000 copper value)
- Beer (1,000 copper value)
- Wine (2,000 copper value)
- Zzar (10,000 copper value)
- Fruit (400 copper value)
- Wheat (100 copper value)
- Corn (100 copper value)
- Fish (600 copper value)
- Oil (1,200 copper value)
- Sugar (2,000 copper value)
- Honey (1,600 copper value)

**Building Materials (29-32):**
- Marble (20 copper value)
- Granite (25 copper value)
- Coal (20 copper value)
- Sulphur (400 copper value)

**Natural Resources (33-37):**
- Timber (50 copper value)
- Silk (400 copper value)
- Wool (100 copper value)
- Linen (40 copper value)
- Furs (500 copper value)

**Manufactured Goods (38-45):**
- Glass (500 copper value)
- Leather (200 copper value)
- Parchment (1,000 copper value)
- Papyrus (600 copper value)
- Dye (100 copper value)
- Coffee (10,000 copper value)
- Tea (5,000 copper value)
- Tobacco (20,000 copper value)

**Industrial Products (46):**
- Industrial Product (10,000 copper value)

## File Format Structure

Each .trd file contains exactly 94 integer values representing:
- Values 0,2,4...92 (even indices): Production rates for commodities 0-46
- Values 1,3,5...93 (odd indices): Consumption rates for commodities 0-46

### Calculation Formula
```c
production = trade_value[commodity * 2] * population / 5000
consumption = trade_value[commodity * 2 + 1] * population / 5000
```

For industrial products, mineral satisfaction affects production:
```c
industrial_production = trade_value[92] * population * mineral_satisf / 5000
```

## Economic Categories

**Minerals (commodities 0-10):** Affect mineral satisfaction
**Food (commodities 14-28):** Affect food satisfaction  
**All Others:** General trade goods

## Proposed Trade Values

### Industrial Economy (Industrial.trd)
Focus: High metal/manufactured goods production, low food production

```
# Metals - High Production, Low Consumption  
5,1,   # Gold: produce 5, consume 1
8,2,   # Silver: produce 8, consume 2  
15,3,  # Copper: produce 15, consume 3
2,1,   # Platinum: produce 2, consume 1
25,5,  # Iron: produce 25, consume 5
20,4,  # Bronze: produce 20, consume 4
3,1,   # Adamantium: produce 3, consume 1
4,1,   # Mithril: produce 4, consume 1
30,6,  # Steel: produce 30, consume 6
15,3,  # Lead: produce 15, consume 3
18,3,  # Tin: produce 18, consume 3

# Gems - Moderate Production
1,1,   # Diamond: produce 1, consume 1
2,1,   # Emerald: produce 2, consume 1  
2,1,   # Ruby: produce 2, consume 1

# Food - Low Production, High Consumption
2,8,   # Meat: produce 2, consume 8
1,6,   # Salt: produce 1, consume 6
0,4,   # Spices: produce 0, consume 4
1,8,   # Milk: produce 1, consume 8
3,6,   # Ale: produce 3, consume 6
3,6,   # Beer: produce 3, consume 6
1,4,   # Wine: produce 1, consume 4
0,2,   # Zzar: produce 0, consume 2
1,6,   # Fruit: produce 1, consume 6
2,10,  # Wheat: produce 2, consume 10
2,10,  # Corn: produce 2, consume 10
1,5,   # Fish: produce 1, consume 5
1,4,   # Oil: produce 1, consume 4
1,3,   # Sugar: produce 1, consume 3
1,3,   # Honey: produce 1, consume 3

# Building Materials - High Production
10,3,  # Marble: produce 10, consume 3
12,3,  # Granite: produce 12, consume 3
25,8,  # Coal: produce 25, consume 8
8,2,   # Sulphur: produce 8, consume 2

# Natural Resources - Variable
5,3,   # Timber: produce 5, consume 3
2,4,   # Silk: produce 2, consume 4
3,5,   # Wool: produce 3, consume 5
4,3,   # Linen: produce 4, consume 3
1,3,   # Furs: produce 1, consume 3

# Manufactured - Very High Production
15,3,  # Glass: produce 15, consume 3
12,4,  # Leather: produce 12, consume 4
6,2,   # Parchment: produce 6, consume 2
5,2,   # Papyrus: produce 5, consume 2
8,2,   # Dye: produce 8, consume 2
2,4,   # Coffee: produce 2, consume 4
2,4,   # Tea: produce 2, consume 4
1,4,   # Tobacco: produce 1, consume 4

# Industrial Products - Very High Production
20,2   # Industrial Product: produce 20, consume 2
```

### Agricultural Economy (Agricoltural.trd) 
Focus: High food production, low industrial production

```
# Metals - Low Production, Moderate Consumption
1,3,   # Gold: produce 1, consume 3
2,4,   # Silver: produce 2, consume 4
5,6,   # Copper: produce 5, consume 6
0,1,   # Platinum: produce 0, consume 1
3,8,   # Iron: produce 3, consume 8
2,6,   # Bronze: produce 2, consume 6
0,1,   # Adamantium: produce 0, consume 1
0,1,   # Mithril: produce 0, consume 1
2,8,   # Steel: produce 2, consume 8
2,4,   # Lead: produce 2, consume 4
3,5,   # Tin: produce 3, consume 5

# Gems - Low Production
0,1,   # Diamond: produce 0, consume 1
1,1,   # Emerald: produce 1, consume 1
1,1,   # Ruby: produce 1, consume 1

# Food - Very High Production, Low Consumption
15,3,  # Meat: produce 15, consume 3
8,2,   # Salt: produce 8, consume 2
6,1,   # Spices: produce 6, consume 1
20,4,  # Milk: produce 20, consume 4
12,3,  # Ale: produce 12, consume 3
10,3,  # Beer: produce 10, consume 3
8,2,   # Wine: produce 8, consume 2
3,1,   # Zzar: produce 3, consume 1
25,5,  # Fruit: produce 25, consume 5
30,6,  # Wheat: produce 30, consume 6
25,5,  # Corn: produce 25, consume 5
12,3,  # Fish: produce 12, consume 3
10,2,  # Oil: produce 10, consume 2
15,3,  # Sugar: produce 15, consume 3
20,4,  # Honey: produce 20, consume 4

# Building Materials - Low Production
2,5,   # Marble: produce 2, consume 5
3,6,   # Granite: produce 3, consume 6
3,6,   # Coal: produce 3, consume 6
1,3,   # Sulphur: produce 1, consume 3

# Natural Resources - High Production
15,3,  # Timber: produce 15, consume 3
4,2,   # Silk: produce 4, consume 2
18,3,  # Wool: produce 18, consume 3
20,4,  # Linen: produce 20, consume 4
8,2,   # Furs: produce 8, consume 2

# Manufactured - Low Production, High Consumption
2,6,   # Glass: produce 2, consume 6
8,3,   # Leather: produce 8, consume 3
3,4,   # Parchment: produce 3, consume 4
2,3,   # Papyrus: produce 2, consume 3
4,3,   # Dye: produce 4, consume 3
1,6,   # Coffee: produce 1, consume 6
2,5,   # Tea: produce 2, consume 5
3,3,   # Tobacco: produce 3, consume 3

# Industrial Products - Very Low Production
1,5    # Industrial Product: produce 1, consume 5
```

### Commercial Economy (Commercial.trd)
Focus: Balanced trade hub with moderate production/consumption

```
# Metals - Balanced Production/Consumption
3,3,   # Gold: produce 3, consume 3
5,5,   # Silver: produce 5, consume 5
10,8,  # Copper: produce 10, consume 8
1,1,   # Platinum: produce 1, consume 1
12,10, # Iron: produce 12, consume 10
10,8,  # Bronze: produce 10, consume 8
2,2,   # Adamantium: produce 2, consume 2
2,2,   # Mithril: produce 2, consume 2
15,12, # Steel: produce 15, consume 12
8,6,   # Lead: produce 8, consume 6
10,8,  # Tin: produce 10, consume 8

# Gems - Balanced
1,1,   # Diamond: produce 1, consume 1
1,1,   # Emerald: produce 1, consume 1
1,1,   # Ruby: produce 1, consume 1

# Food - Balanced
8,8,   # Meat: produce 8, consume 8
4,4,   # Salt: produce 4, consume 4
3,3,   # Spices: produce 3, consume 3
10,10, # Milk: produce 10, consume 10
6,6,   # Ale: produce 6, consume 6
6,6,   # Beer: produce 6, consume 6
4,4,   # Wine: produce 4, consume 4
2,2,   # Zzar: produce 2, consume 2
12,10, # Fruit: produce 12, consume 10
15,15, # Wheat: produce 15, consume 15
15,15, # Corn: produce 15, consume 15
8,8,   # Fish: produce 8, consume 8
6,6,   # Oil: produce 6, consume 6
8,6,   # Sugar: produce 8, consume 6
10,8,  # Honey: produce 10, consume 8

# Building Materials - Balanced
6,6,   # Marble: produce 6, consume 6
8,8,   # Granite: produce 8, consume 8
12,10, # Coal: produce 12, consume 10
4,4,   # Sulphur: produce 4, consume 4

# Natural Resources - Balanced
10,8,  # Timber: produce 10, consume 8
3,3,   # Silk: produce 3, consume 3
8,8,   # Wool: produce 8, consume 8
10,8,  # Linen: produce 10, consume 8
4,4,   # Furs: produce 4, consume 4

# Manufactured - Balanced
8,8,   # Glass: produce 8, consume 8
8,6,   # Leather: produce 8, consume 6
4,4,   # Parchment: produce 4, consume 4
3,3,   # Papyrus: produce 3, consume 3
6,5,   # Dye: produce 6, consume 5
2,4,   # Coffee: produce 2, consume 4
3,4,   # Tea: produce 3, consume 4
2,4,   # Tobacco: produce 2, consume 4

# Industrial Products - Moderate
8,6    # Industrial Product: produce 8, consume 6
```

### Mineral Economy (Mineral.trd)
Focus: High raw material production, especially metals and gems

```
# Metals - Very High Production, Low Consumption
8,2,   # Gold: produce 8, consume 2
12,3,  # Silver: produce 12, consume 3
20,4,  # Copper: produce 20, consume 4
3,1,   # Platinum: produce 3, consume 1
30,5,  # Iron: produce 30, consume 5
25,4,  # Bronze: produce 25, consume 4
5,1,   # Adamantium: produce 5, consume 1
6,1,   # Mithril: produce 6, consume 1
35,6,  # Steel: produce 35, consume 6
20,3,  # Lead: produce 20, consume 3
22,3,  # Tin: produce 22, consume 3

# Gems - High Production
3,1,   # Diamond: produce 3, consume 1
4,1,   # Emerald: produce 4, consume 1
4,1,   # Ruby: produce 4, consume 1

# Food - Very Low Production, High Consumption
1,10,  # Meat: produce 1, consume 10
0,8,   # Salt: produce 0, consume 8
0,5,   # Spices: produce 0, consume 5
1,12,  # Milk: produce 1, consume 12
2,8,   # Ale: produce 2, consume 8
2,8,   # Beer: produce 2, consume 8
0,5,   # Wine: produce 0, consume 5
0,3,   # Zzar: produce 0, consume 3
1,8,   # Fruit: produce 1, consume 8
1,12,  # Wheat: produce 1, consume 12
1,12,  # Corn: produce 1, consume 12
1,6,   # Fish: produce 1, consume 6
1,5,   # Oil: produce 1, consume 5
0,4,   # Sugar: produce 0, consume 4
1,4,   # Honey: produce 1, consume 4

# Building Materials - Very High Production
15,2,  # Marble: produce 15, consume 2
18,3,  # Granite: produce 18, consume 3
30,5,  # Coal: produce 30, consume 5
12,2,  # Sulphur: produce 12, consume 2

# Natural Resources - Low Production
3,5,   # Timber: produce 3, consume 5
1,4,   # Silk: produce 1, consume 4
2,6,   # Wool: produce 2, consume 6
2,5,   # Linen: produce 2, consume 5
6,2,   # Furs: produce 6, consume 2

# Manufactured - Low Production, High Consumption
3,8,   # Glass: produce 3, consume 8
4,6,   # Leather: produce 4, consume 6
2,5,   # Parchment: produce 2, consume 5
1,4,   # Papyrus: produce 1, consume 4
3,4,   # Dye: produce 3, consume 4
0,5,   # Coffee: produce 0, consume 5
1,5,   # Tea: produce 1, consume 5
1,5,   # Tobacco: produce 1, consume 5

# Industrial Products - Low Production
2,8    # Industrial Product: produce 2, consume 8
```

### Hybrid Economies

**Industrial-Commercial (IndComm.trd):** 75% Industrial + 25% Commercial values
**Industrial-Mineral (IndMin.trd):** 60% Industrial + 40% Mineral values  
**Mineral-Agricultural (MinAgr.trd):** 50% Mineral + 50% Agricultural values
**Mineral-Commercial (MinComm.trd):** 60% Mineral + 40% Commercial values
**Agricultural-Commercial (AgrComm.trd):** 70% Agricultural + 30% Commercial values

## Economic Balance Considerations

1. **Population Scaling**: All values scale with population (÷5000), so base values represent production per 5000 population
2. **Mineral Satisfaction**: Affects industrial production - insufficient minerals reduce industrial output
3. **Food Satisfaction**: Affects population growth and trade center health
4. **Price Dynamics**: Prices fluctuate based on supply/demand ratio using the formula in `get_commodity_value()`
5. **Virtual NPCs**: The system includes virtual NPC traders that buy/sell based on market conditions

## Implementation Recommendations

1. Start with moderate values and adjust based on gameplay testing
2. Monitor logs for economic imbalances (overproduction/shortages)
3. Ensure hybrid economies create meaningful trade relationships
4. Balance luxury goods (gems, coffee, tobacco) as high-value, low-volume trades
5. Keep food production sufficient to prevent starvation but not oversupply
6. Industrial products should require mineral inputs to function properly

The proposed values create a realistic medieval fantasy economy where different towns have clear specializations and interdependencies, encouraging player trade and economic gameplay.

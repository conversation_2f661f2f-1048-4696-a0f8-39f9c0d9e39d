SHOP: 59130
HOURS: 8-12 13-17 18-20
ROOM: 59547
GREED: 140       ;; sale prices will be 140% of an object's value.
PROFIT: 90       ;; buy prices will be 73% of object's value.
                 ;; calculation:  140 / (100 + 90) = .7368 (truncated)

CASTING:         ;; anyone can cast spells in this shop
;; this shopkeeper cannot be attacked
DEADBEAT: 1      ;; not really used yet.
OFFENSE: 2       ;; not really used yet.

CHEATS: GOODS   ;; everyone of good race
;; Buy price for goods is 280%, Sale price 36% of object's value.

HATES: NPC
;; won't trade with NPCs

;; produces objects with these Vnums
PO: 59121 
PO: 59122 
PO: 59123 
PO: 59124 
PO: 59125 
PO: 59126 
PO: 59127          

BT: 5        ;; shop will buy weapons

MBCASH: $n says 'Don't make me put these to use on you, fool...'
MBHAVE: $n says 'If you try my patience, you will regret it. You don't have that.'
MBIGOT: $n says 'Leave now, or die!'
MBUY:   $n says 'Here's your %s.'
MCLOSE: $n says 'I am now closed. Come back after %s.'
MNBUY:  $n says '$N I have no need for such junk as $p. Take it elsewhere.'
MOPEN:  $n stretches his arms, walks up to his anvil and says 'I am now open for business.'
MSCASH: $n says 'Too costly for me $N. Too much ornamental. Bet it's not as fine as mine.'
MSELL:  $n says 'Sold. $N, here's your $p, now go and cleave someone's head with it.'
MSHAVE: $n looks around angrily. He says 'Are you blind $N? I sell what you see.'

;;END OF SHOP NR. 59130

SHOP: 59147
HOURS: 8-12 13-17 18-20
ROOM: 59177
GREED: 140       ;; sale prices will be 140% of an object's value.
PROFIT: 90       ;; buy prices will be 73% of object's value.
                 ;; calculation:  140 / (100 + 90) = .7368 (truncated)
CASTING:         ;; anyone can cast spells in this shop
;; this shopkeeper cannot be attacked
DEADBEAT: 1      ;; not really used yet.
OFFENSE: 2       ;; not really used yet.

;; does not cheat anyone

HATES: NPC
;; won't trade with NPCs

;; produces objects with these Vnums
PO: 59103 
PO: 59104 
PO: 59106 
PO: 59105    
PO: 59220

BT: 2
BT: 3 
BT: 4 
BT: 10        ;; shop will buy scrolls, wands, staves and potions

MBCASH: $n says 'Sorry, you can't afford it!'
MBHAVE: $n says 'Sorry $N, but you don't seem to have that.'
MBIGOT: $n says 'I'm sorry but I don't deal with people like you!'
MBUY:   $n says 'Here's your %s.'
MCLOSE: $n says 'I am now closed. Come back after %s.'
MNBUY:  $n says '$N I don't need $p. Sorry, try elsewhere in town.'
MOPEN:  $n says 'I am now open for business!'
MSCASH: $n says 'Sorry $N, I don't have that kind of money at the moment!'
MSELL:  $n says 'Sold. $N, here's your $p, enjoy and be sure to return for more.'
MSHAVE: $n says 'Sorry, I don't make or sell those.'

;;END OF SHOP NR. 59147

SHOP: 59148
HOURS: 8-12 13-17 18-20
ROOM: 59294
GREED: 140       ;; sale prices will be 140% of an object's value.
PROFIT: 90       ;; buy prices will be 73% of object's value.
                 ;; calculation:  140 / (100 + 90) = .7368 (truncated)
CASTING:         ;; anyone can cast spells in this shop
;; this shopkeeper cannot be attacked
DEADBEAT: 1      ;; not really used yet.
OFFENSE: 2       ;; not really used yet.

;; does not cheat anyone

HATES: NPC
;; won't trade with NPCs

;; produces objects with these Vnums
PO: 59112 
PO: 59113 
PO: 59114 
PO: 59115    
PO: 59116    

;; shop does not buy anything

MBCASH: $n says 'Sorry, you can't afford it!'
MBHAVE: $n says 'Sorry $N, but you don't seem to have anything like that.'
MBIGOT: $n says 'I'm sorry but I don't deal with people like you!'
MBUY:   $n says 'Here's your %s.'
MCLOSE: $n says 'Sorry, but even I have to get some sleep! Come back after %s.'
MNBUY:  $n says '$N I don't need $p. Sorry, try elsewhere in town.'
MOPEN:  $n says 'I am now open for business, please have a seat!'
MSCASH: $n says 'Sorry $N, I don't have that kind of money at the moment!'
MSELL:  $n says 'Sold. $N - a fine choice indeed, $p. Call me if you require anything else! '
MSHAVE: $n says 'Sorry friend, we don't have those at this time.'

;;END OF SHOP NR. 59148

SHOP: 59164
HOURS: 8-12 13-17 18-20
ROOM: 59361
GREED: 140       ;; sale prices will be 140% of an object's value.
PROFIT: 90       ;; buy prices will be 73% of object's value.
                 ;; calculation:  140 / (100 + 90) = .7368 (truncated)
CASTING:         ;; anyone can cast spells in this shop
;; this shopkeeper cannot be attacked
DEADBEAT: 1      ;; not really used yet.
OFFENSE: 2       ;; not really used yet.

;; does not cheat anyone

HATES: NPC
;; won't trade with NPCs

;; produces objects with these Vnums
PO: 59128 
PO: 59129 
PO: 59130 
PO: 59131    
PO: 59132 
PO: 59133 
PO: 59134     

;; shop does not buy anything

MBCASH: $n says 'Come back when you have more money, pauper!'
MBHAVE: $n says 'Listen %N, if you try to swindle me, the guards will be here in no time.'
MBIGOT: $n says 'Your type is not welcome on our ships. Begone!'
MBUY:   $n says 'Here's your %s.'
MCLOSE: $n says 'We're now closed. Come back at %s.'
MNBUY:  $n says '$N I don't need $p. Sorry, try elsewhere in town.'
MOPEN:  $n says 'The Fallen Stars is now open for business, step right up.'
MSCASH: $n says 'Sorry $N, I don't have that kind of money at the moment!'
MSELL:  $n says 'Alright, $N it will be. Enjoy your trip, $p.'
MSHAVE: $n says 'Sorry friend, we don't have those at this time.'
;;END OF SHOP NR. 59164

SHOP: 59181
HOURS: 8-12 13-17 18-20
ROOM: 59253
GREED: 140       ;; sale prices will be 140% of an object's value.
PROFIT: 90       ;; buy prices will be 73% of object's value.
                 ;; calculation:  140 / (100 + 90) = .7368 (truncated)
CASTING:         ;; anyone can cast spells in this shop
;; this shopkeeper cannot be attacked
DEADBEAT: 1      ;; not really used yet.
OFFENSE: 2       ;; not really used yet.

;; does not cheat anyone

HATES: NPC
;; won't trade with NPCs

;; produces objects with these Vnums
PO: 59107 
PO: 59108 
PO: 59109 
PO: 59110    
PO: 59111    

BT: 11 
BT: 9   ;; shop buys armor and clothing

MBCASH: $n says 'If you're trying to swindle me, one yell will bring the guards here!'
MBHAVE: $n says 'Hey $N, you can't SELL what you don't HAVE.'
MBIGOT: $n says 'I don't do business with your type.'
MBUY:   $n says 'Sold - here's your %s.'
MCLOSE: $n says 'We're now closed. Come back at %s.'
MNBUY:  $n says '$N I don't need $p. Sorry, try elsewhere in town.'
MOPEN:  $n says 'I am now open for business.'
MSCASH: $n says 'Sorry $N, I don't have that kind of money at the moment!'
MSELL:  $n says '$p - A fine choice $N, tell all your friends you bought it here.'
MSHAVE: $n says 'Sorry, we don't have those at this time.'
;;END OF SHOP NR. 59181

SHOP: 59202
HOURS: 8-12 13-17 18-20
ROOM: 59243
GREED: 140       ;; sale prices will be 140% of an object's value.
PROFIT: 90       ;; buy prices will be 73% of object's value.
                 ;; calculation:  140 / (100 + 90) = .7368 (truncated)
CASTING:         ;; anyone can cast spells in this shop
;; this shopkeeper cannot be attacked
DEADBEAT: 1      ;; not really used yet.
OFFENSE: 2       ;; not really used yet.

;; does not cheat anyone

HATES: NPC
;; won't trade with NPCs

;; produces objects with these Vnums
PO: 59117 
PO: 59118 
PO: 59119 
PO: 59120    

;; shop buys nothing

MBCASH: $n says 'If you're trying to swindle me, one yell will bring the guards here!'
MBHAVE: $n says '$N, you don't have that.'
MBIGOT: $n says 'I don't do business with your type.'
MBUY:   $n says 'Sold - here's your %s.'
MCLOSE: $n says 'We're now closed, the guests need some quiet. Come back at %s.'
MNBUY:  $n says '$N I don't need $p. Sorry, try elsewhere in town.'
MOPEN:  $n says 'The Shield is now open for business. '
MSCASH: $n says 'Sorry $N, I don't have that kind of money at the moment!'
MSELL:  $n says '$p - A fine choice $N, tell all your friends you bought it here.'
MSELL:  $n says '$N - a fine taste you have, this is a great $p.'
MSHAVE: $n says 'Sorry, we don't have those at this time.'
;;END OF SHOP NR. 59202

SHOP: 59221
ROAMING:
HOURS: 8-12 13-17 18-20
ROOM: 0
GREED: 140       ;; sale prices will be 140% of an object's value.
PROFIT: 90       ;; buy prices will be 73% of object's value.
                 ;; calculation:  140 / (100 + 90) = .7368 (truncated)
CASTING:         ;; anyone can cast spells in this shop
;; this shopkeeper cannot be attacked
DEADBEAT: 1      ;; not really used yet.
OFFENSE: 2       ;; not really used yet.

;; does not cheat anyone

HATES: NPC
;; won't trade with NPCs

PO: 59138   ;; produces objects with this Vnums

;; shop buys nothing

MBCASH: $n says 'I'm a hard working poor man, go rob a noble instead!'
MBHAVE: $n says '$N, try selling me something you DO have, alright?.'
MBIGOT: $n says 'Your type has given me trouble once too many.'
MBUY:   $n says 'Sold - here's your %s.'
MCLOSE: $n says 'I've got to get going, come back at %s.'
MNBUY:  $n says '$N I don't need $p. Sorry, try somewhere else.'
MOPEN:  $n says 'Step right up and get your fine grain, I'm now ready for business.'
MSCASH: $n says 'Sorry $N, I don't have that kind of money!'
MSELL:  $n says 'I saw the production of this $N from crop to grain! That will be $p.'
MSHAVE: $n says 'Sorry, I don't have that kind of stuff.'
;;END OF SHOP NR. 59221

SHOP: 59228
HOURS: 8-12 13-17 18-20
ROOM: 59597
GREED: 140       ;; sale prices will be 140% of an object's value.
PROFIT: 90       ;; buy prices will be 73% of object's value.
                 ;; calculation:  140 / (100 + 90) = .7368 (truncated)
CASTING:         ;; anyone can cast spells in this shop
;; this shopkeeper cannot be attacked
DEADBEAT: 1      ;; not really used yet.
OFFENSE: 2       ;; not really used yet.

;; does not cheat anyone

HATES: NPC
;; won't trade with NPCs

;; produces objects with these Vnums
PO: 59170 
PO: 59171 
PO: 59172 
PO: 59173
PO: 59169    

;; shop buys armor and clothing
BT: 11
BT: 9

MBCASH: $n says 'If you're trying to swindle me, one yell will bring the guards here!'
MBHAVE: $n says 'Sorry, but you don't seem to have that $N.'
MBIGOT: $n says 'I don't do business with your type.'
MBUY:   $n says 'Sold - here's your %s.'
MCLOSE: $n says 'I am now closed. Come back at %s.'
MNBUY:  $n says '$N I don't need $p. Sorry, try elsewhere in town.'
MOPEN:  $n says 'I am now open for business.'
MSCASH: $n says 'Sorry $N, I don't have that kind of money at the moment!'
MSELL:  $n says '$p - A fine choice $N, tell all your friends you bought it here.'
MSHAVE: $n says 'Sorry, I don't make or sell that kind of stuff.'
;;END OF SHOP NR. 59228

SHOP: 59229
HOURS: 8-12 13-17 18-20
ROOM: 59414
GREED: 140       ;; sale prices will be 140% of an object's value.
PROFIT: 90       ;; buy prices will be 73% of object's value.
                 ;; calculation:  140 / (100 + 90) = .7368 (truncated)
CASTING:         ;; anyone can cast spells in this shop
;; this shopkeeper cannot be attacked
DEADBEAT: 1      ;; not really used yet.
OFFENSE: 2       ;; not really used yet.

;; does not cheat anyone

HATES: NPC
;; won't trade with NPCs

;; produces objects with these Vnums
PO: 59222 
PO: 59223 
PO: 59224 

;; shop buys nothing

MBCASH: $n says 'Come on, I've got enough trouble as it is. Go beg elsewhere.'
MBHAVE: $n says 'You must have had one ale too many. You don't have that.'
MBIGOT: $n says 'I don't do business with your kind, please leave or I will call the guards.'
MBUY:   $n says 'Sold - here's your %s.'
MCLOSE: $n says 'I am now closed. Come back at %s.'
MNBUY:  $n says '$N I don't need $p. Sorry, try elsewhere in town.'
MOPEN:  $n says 'I am now open for business.'
MSCASH: $n says 'Sorry $N, I don't have that kind of money at the moment!'
MSELL:  $n says 'Enjoy your $p, $N.'
MSHAVE: $n says 'Sorry, I just make crates.'

;;END OF SHOP NR. 59229

SHOP: 59230
HOURS: 8-12 13-17 18-20
ROOM: 59411
GREED: 140       ;; sale prices will be 140% of an object's value.
PROFIT: 90       ;; buy prices will be 73% of object's value.
                 ;; calculation:  140 / (100 + 90) = .7368 (truncated)
CASTING:         ;; anyone can cast spells in this shop
;; this shopkeeper cannot be attacked
DEADBEAT: 1      ;; not really used yet.
OFFENSE: 2       ;; not really used yet.

;; does not cheat anyone

HATES: NPC
;; won't trade with NPCs

;; produces objects with these Vnums
PO: 59225 
PO: 59226 
PO: 59227 
PO: 59228    

BT: 11   ;; shop buys clothing

MBCASH: $n says 'If you're trying to swindle me, the guards are nearby!'
MBHAVE: $n says 'Sorry $N, but you don't seem to have that.'
MBIGOT: $n says 'I don't do business with your type.'
MBUY:   $n says 'Sold - here's your %s.'
MCLOSE: $n says 'I am now closed. Come back at %s.'
MNBUY:  $n says '$N I don't need $p. Sorry, try elsewhere in town.'
MOPEN:  $n says 'I am now open for business.'
MSCASH: $n says 'Sorry $N, I don't have that kind of money at the moment!'
MSELL:  $n says '$p - A fine choice $N, tell all your friends you bought it here.'
MSHAVE: $n says 'I only sell the finest clothing. Try elsewhere.'
;;END OF SHOP NR. 59230

SHOP: 59262
HOURS: 8-12 13-17 18-20
ROOM: 59368
GREED: 140       ;; sale prices will be 140% of an object's value.
PROFIT: 90       ;; buy prices will be 73% of object's value.
                 ;; calculation:  140 / (100 + 90) = .7368 (truncated)
CASTING:         ;; anyone can cast spells in this shop
;; this shopkeeper cannot be attacked
DEADBEAT: 1      ;; not really used yet.
OFFENSE: 2       ;; not really used yet.

;; does not cheat anyone

HATES: NPC
;; won't trade with NPCs

;; produces objects with these Vnums
PO: 59139 
PO: 59141 
PO: 59120  

;; shop buys nothing

MBCASH: $n says 'Come back when you have more money!'
MBHAVE: $n says 'Great, another crazy trying to sell me air. Come back when you have it.'
MBIGOT: $n says 'I don't do business with your kind.'
MBUY:   $n says 'Sold - here's your %s.'
MCLOSE: $n says 'I am now closed. Come back at %s.'
MNBUY:  $n says '$N I don't need $p. Sorry, try elsewhere in town.'
MOPEN:  $n says 'I am now open for business.'
MSCASH: $n says 'Sorry $N, I don't have that kind of money at the moment!'
MSELL:  $n says 'Here you go $N, and only %s too!'
MSHAVE: $n says 'I only sell what you see!'
;;END OF SHOP NR. 59262


SHOP: 59276
HOURS: 8-12 13-17 18-20
ROOM: 59371
GREED: 140       ;; sale prices will be 140% of an object's value.
PROFIT: 90       ;; buy prices will be 73% of object's value.
                 ;; calculation:  140 / (100 + 90) = .7368 (truncated)
CASTING:         ;; anyone can cast spells in this shop
;; this shopkeeper cannot be attacked
DEADBEAT: 1      ;; not really used yet.
OFFENSE: 2       ;; not really used yet.

;; does not cheat anyone

HATES: NPC
;; won't trade with NPCs

;; produces objects with these Vnums
PO: 59142 
PO: 59143 
PO: 59144    

;; shop buys nothing

MBCASH: $n says 'Come back when you have more money!'
MBHAVE: $n says 'Come back when you actually have it.'
MBIGOT: $n says 'I don't do business with your kind.'
MBUY:   $n says 'Sold - here's your %s.'
MCLOSE: $n says 'I'm off now, come back at %s.'
MNBUY:  $n says '$N I don't need $p. Sorry, try elsewhere in town.'
MOPEN:  $n says 'I am now open for business.'
MSCASH: $n says 'Sorry $N, I don't have that kind of money at the moment!'
MSELL:  $n says 'Here you go $N, and only %s too. Finest quality, or you can come back!'
MSHAVE: $n says 'I only sell the stuff you see lying around...'
;;END OF SHOP NR. 59276


SHOP: 59267
ROAMING:
HOURS: 8-12 13-17 18-20
ROOM: 0
GREED: 140       ;; sale prices will be 140% of an object's value.
PROFIT: 90       ;; buy prices will be 73% of object's value.
                 ;; calculation:  140 / (100 + 90) = .7368 (truncated)
CASTING:         ;; anyone can cast spells in this shop
;; this shopkeeper cannot be attacked
DEADBEAT: 1      ;; not really used yet.
OFFENSE: 2       ;; not really used yet.

;; does not cheat anyone

HATES: NPC
;; won't trade with NPCs

;; produces objects with these Vnums
PO: 59229 
PO: 59230 
PO: 59231    

;; shop buys nothing

MBCASH: $n says 'Come back when you have more money!'
MBHAVE: $n says '$N, come back when you actually have it.'
MBIGOT: $n says 'I don't do business with your kind.'
MBUY:   $n says 'Sold - here's your %s.'
MCLOSE: $n says 'I'm off now, come back at %s.'
MNBUY:  $n says '$N I don't need $p. Sorry, try elsewhere in town.'
MOPEN:  $n says 'I am now open for business.'
MSCASH: $n says 'Sorry $N, I don't have that kind of money at the moment!'
MSELL:  $n says 'Here you go $N, and only %s too!'
MSHAVE: $n says 'I only sell what you see!'
;;END OF SHOP NR. 59267

SHOP: 59389
ROAMING:
HOURS: 8-12 13-17 18-20
ROOM: 0
GREED: 140       ;; sale prices will be 140% of an object's value.
PROFIT: 90       ;; buy prices will be 73% of object's value.
                 ;; calculation:  140 / (100 + 90) = .7368 (truncated)
CASTING:         ;; anyone can cast spells in this shop
;; this shopkeeper cannot be attacked
DEADBEAT: 1      ;; not really used yet.
OFFENSE: 2       ;; not really used yet.

;; does not cheat anyone

HATES: NPC
;; won't trade with NPCs

;; produces objects with these Vnums
PO: 59289
PO: 59290 
PO: 59291 
PO: 59292    

;;shop buys nothing

MBCASH: $n says 'I'm sorry, it costs more than you have.'
MBHAVE: $n says 'Sorry $N, but you don't seem to have that.'
MBIGOT: $n says 'I'm sorry but I don't deal with people like you!'
MBUY:   $n says 'Here's your %s.'
MCLOSE: $n says 'I'm tired, leaving for now. Come back tomorrow at %s.'
MNBUY:  $n says '$N I don't need $p. Sorry, try elsewhere in town.'
MOPEN:  $n says 'Step right up people, fine utentils!'
MSCASH: $n says 'Sorry $N, I don't have that kind of money at the moment!'
MSELL:  $n says 'Sold. $N, enjoy this $p.'
MSHAVE: $n says 'Sorry, I don't sell that.'

;;END OF SHOP NR. 59389

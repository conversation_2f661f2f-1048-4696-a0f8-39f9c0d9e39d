# Developer Guide

Technical architecture, code layout, and practices for Outcast MUD. This aligns with the current build system: the Makefile compiles and links zlib to produce the binary named `ocm_new`.

## Table of Contents

1. [Architecture Overview](#architecture)
2. [Code Structure](#code-structure)
3. [Core Systems](#core-systems)
4. [Data Structures](#data-structures)
5. [World File Formats](#file-formats)
6. [Building and Compilation](#building-and-compilation)
7. [Feature Development](#feature-development)
8. [Debugging](#debugging)
9. [Best Practices](#best-practices)
10. [Common Modifications](#common-modifications)
11. [Testing](#testing)

## Architecture Overview {#architecture}

Classic Diku/Circle lineage with extensive customizations. High-level flow:

```
TCP Listener -> Connection Manager -> Game Loop (input -> process -> output) -> World State
```

Key subsystems:
- Network with Telnet and MCCP (zlib)
- Command interpreter
- World management (rooms, objects, characters)
- Event/timer system
- Combat and magic
- File I/O for world and player persistence
- Special procedures (area hooks)

## Code Structure

Note: Filenames in this distribution differ from canonical Circle; refer to src listing. Examples:
- Core: comm.c, interp.c, db.c, handler.c, limits.c, files.c
- Systems: magic.c, combat.o (integrated across files), socials.c, spells.*, specials.c
- Features: assocs/associations, trade, ship, justice/newjustice, kingdom/housing, contracts
- Special procedures: src/specs.*.c and specs.assign.c manage assignments

Headers of interest in this tree:
- comm.h, db.h, interp.h, spells.h, kingdom.h, justice.h, race_class.h, mccp.h

## Core Systems

### Game Loop (comm.c)

Pseudocode:
```c
while (!shutdown) {
  check_new_connections();
  process_input();
  process_commands();
  update_world();     // combat ticks, events, effects
  process_output();   // color, paging, compression
  sleep_until_next_pulse();
}
```

### Command Interpreter (interp.c / interp.h)

Command table maps strings to handlers and privilege/position data:
```c
struct command_info {
  const char *command;
  byte minimum_position;
  void (*command_pointer)(CHAR_DATA *ch, char *argument);
  sh_int minimum_level;
  byte subcmd;
};
```

Add commands by defining ACMD handlers and registering in the table.

### MCCP (mccp.c / mccp.h)

Zlib-based compression negotiated via Telnet COMPRESS2. Ensure output is funneled through compression layer when active.

## Data Structures

Representative examples (names may vary by headers in this tree):

Character:
```c
struct char_data {
  char *name, *short_descr, *long_descr;
  struct char_ability_data abilities;
  struct char_point_data points;   // hp, mana, move, armor
  byte position;
  long idnum;
  int in_room;
  struct obj_data *carrying;
  struct char_data *next_in_room;
  struct descriptor_data *desc;
  struct affected_type *affected;
  struct char_data *fighting;
  byte damroll, hitroll;
};
```

Object:
```c
struct obj_data {
  obj_vnum vnum;
  char *name, *short_description, *description;
  byte type;
  int wear_flags;
  int value[4];
  byte weight;
  int cost;
  struct obj_data *next_content, *contains;
  struct char_data *carried_by, *worn_by;
  int worn_on;
};
```

Room:
```c
struct room_data {
  room_vnum vnum;
  char *name, *description;
  byte sector_type;
  long room_flags;
  struct room_direction_data *dir_option[NUM_OF_DIRS];
  struct char_data *people;
  struct obj_data *contents;
  int (*func)(struct char_data *, void *, int, char *);
};
```

## World File Formats {#file-formats}

Matches Circle-style .wld, .mob, .obj with Outcast-specific extensions via specs and scripts. See docs/area-building.md for field meanings.

## Building and Compilation

Requirements:
- GCC/clang on Linux/Unix
- zlib (links with -lz)
- MySQL optional and disabled by default

Build:
```bash
cd src
make clean
make
ls -la ocm_new
```

Makefile highlights:
- DEBUG uses -ggdb3 -rdynamic
- Many -Wno-* flags enabled
- LIBS = -lz
- Target: ocm_new

If you change the output name, update docs and scripts accordingly.

## Feature Development {#feature-development}

### Add a Command

1) Implement handler in suitable module (e.g., act*.c):
```c
ACMD(do_wave) {
  send_to_char("You wave.\r\n", ch);
}
```

2) Declare in interp.h:
```c
ACMD(do_wave);
```

3) Register in interp.c:
```c
{ "wave", POS_STANDING, do_wave, 0, 0 },
```

### Add a Skill

- Define in a skills/spells header (e.g., skillrec.h or spells.h)
- Register metadata in constants/skill tables (e.g., constant.c)
- Implement effect and integrate with practice/training flows

### Add a Spell

- Define number in spells.h
- Metadata in spells.c/constant.c
- Implement effect in magic.c
- Add to parser/dispatcher in interpreter/magic systems

### Special Procedures

Implement and assign:
```c
SPECIAL(my_proc) {
  if (CMD_IS("say")) { /* ... */ return TRUE; }
  return FALSE;
}

/* specs.assign.c */
ASSIGNMOB(1234, my_proc);
/* ASSIGNOBJ / ASSIGNROOM similarly */
```

## Debugging

Logging and diagnostics:
```c
mudlog("info: %s in room %d", GET_NAME(ch), IN_ROOM(ch));
```

GDB:
```bash
gdb ./ocm_new
run 4000
bt
frame N
print var
```

Valgrind:
```bash
valgrind --leak-check=full ./ocm_new 4000
```

Common pitfalls:
- Null checks on descriptors/chars/objs
- Bounds validation for rooms/arrays
- String buffer sizes and snprintf usage
- Event re-entrancy and state cleanup

## Best Practices

- Keep functions focused; refactor long handlers
- Prefer const-correctness and static for internal linkage
- Free all allocations; set pointers to NULL after free
- Centralize repeated logic (helpers/utilities)
- Guard feature flags with compile-time options where practical

Performance:
- Avoid repeated string formatting in hot paths
- Use hash maps/indices for frequent lookups
- Profile hotspots before micro-optimizing

## Common Modifications

Balance:
- limits.c for regen/leveling
- combat engine files for hit/damage
- magic.c for spells
- Adjust world data for item stats

Races/classes:
- race_class.h additions
- constant.c tables
- nanny.c creation menus
- Sprinkle rules where class/race gates apply

Messages:
- lib/misc/messages, lib/misc/socials, help files under lib/information

## Testing

Unit-like harnesses can be built as small test mains compiled against select modules. Example:
```c
int main(void) {
  /* init minimal world context */
  /* call function under test */
  /* assert outcomes */
}
```

Integration manual checklist:
- Commands parse and gate by position/level
- Spells/skills function across edge cases
- Zones load/reset as expected
- MCCP negotiation and compression stable
- No leaks under connection churn

Load:
- Script multiple telnet clients
- Exercise combat and message throughput
- Watch memory and CPU

This guide reflects the current repository (Makefile target ocm_new; zlib linked; MySQL disabled). Update sections accordingly if you change the build, dependencies, or major subsystems.